<?php
/**
 * Template Name: Vehicle Professionals
 * Template for displaying the main Vehicle Professionals page
 *
 * @package AutoHub_Zambia
 */

get_header();

// Add custom CSS for filter hints
?>
<style>
    .filter-hint {
        font-size: 0.65rem;
        color: #6b7280;
        background-color: #f3f4f6;
        padding: 1px 4px;
        border-radius: 3px;
        white-space: nowrap;
    }
    
    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    
    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }
    
    /* Hide filter hints on mobile */
    @media (max-width: 640px) {
        .filter-hint {
            display: none;
        }
    }
</style>
<?php

// Function to get cities with vehicle professional counts
function get_cities_with_professional_counts() {
    global $wpdb;
    
    // Get all cities that have professionals directly from the database
    // This query handles the serialized array format of city_town values
    $cities_with_counts = $wpdb->get_results("
        SELECT 
            p.ID,
            p.post_title as city_name,
            COUNT(DISTINCT pm.post_id) as professional_count
        FROM 
            {$wpdb->posts} p
        JOIN 
            {$wpdb->postmeta} pm ON (
                pm.meta_key = 'city_town' AND
                (
                    pm.meta_value = p.ID OR
                    pm.meta_value LIKE CONCAT('a:1:{i:0;s:', LENGTH(p.ID), ':\"', p.ID, '\";%') OR
                    pm.meta_value LIKE CONCAT('%\"', p.ID, '\"%')
                )
            )
        JOIN 
            {$wpdb->posts} pro ON (pm.post_id = pro.ID AND pro.post_type = 'vehicle_professional' AND pro.post_status = 'publish')
        WHERE 
            p.post_type = 'city_town'
            AND p.post_status = 'publish'
        GROUP BY 
            p.ID, p.post_title
        HAVING 
            professional_count > 0
        ORDER BY
            CASE WHEN LOWER(p.post_title) = 'lusaka' THEN 0 ELSE 1 END,
            p.post_title ASC
    ");
    
    // If the SQL approach didn't work, try a PHP approach
    if (empty($cities_with_counts)) {
        // Get all published vehicle professionals
        $professionals = get_posts([
            'post_type' => 'vehicle_professional',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'fields' => 'ids'
        ]);
        
        if (empty($professionals)) {
            return [];
        }
        
        $city_counts = [];
        
        // Loop through each professional and get its city_town
        foreach ($professionals as $pro_id) {
            $city_town = get_post_meta($pro_id, 'city_town', true);
            
            // Handle serialized array format
            if (is_string($city_town) && strpos($city_town, 'a:') === 0) {
                $city_town = maybe_unserialize($city_town);
            }
            
            // Extract city ID
            $city_id = null;
            if (is_array($city_town) && !empty($city_town)) {
                $city_id = reset($city_town); // Get first element
            } elseif (is_numeric($city_town)) {
                $city_id = $city_town;
            }
            
            // Count the city if we found a valid ID
            if ($city_id && is_numeric($city_id)) {
                $city_id = intval($city_id);
                if (!isset($city_counts[$city_id])) {
                    $city_counts[$city_id] = 0;
                }
                $city_counts[$city_id]++;
            }
        }
        
        if (empty($city_counts)) {
            return [];
        }
        
        // Get city details for cities that have professionals
        $city_ids_string = implode(',', array_map('intval', array_keys($city_counts)));
        
        $cities_with_counts = $wpdb->get_results("
            SELECT
                ct.ID,
                ct.post_title as city_name
            FROM {$wpdb->posts} ct
            WHERE ct.ID IN ({$city_ids_string})
            AND ct.post_type = 'city_town'
            AND ct.post_status = 'publish'
            ORDER BY
                CASE WHEN LOWER(ct.post_title) = 'lusaka' THEN 0 ELSE 1 END,
                ct.post_title ASC
        ");
        
        // Add the counts to the results
        foreach ($cities_with_counts as $city) {
            $city->professional_count = $city_counts[$city->ID];
        }
    }
    
    return $cities_with_counts;
}

// Function to get professional types with counts
function get_professional_types_with_counts($selected_city = '') {
    global $wpdb;
    
    $city_filter = '';
    if ($selected_city) {
        $city_id = intval($selected_city);
        
        // Use a subquery to find professionals with the selected city
        // This handles the serialized array format of city_town values
        $city_filter = $wpdb->prepare("
            AND vp.ID IN (
                SELECT DISTINCT post_id 
                FROM {$wpdb->postmeta} 
                WHERE meta_key = 'city_town'
                AND (
                    meta_value = %s OR
                    meta_value LIKE %s OR
                    meta_value LIKE %s
                )
            )
        ", 
        $city_id,
        'a:1:{i:0;s:' . strlen($city_id) . ':"' . $city_id . '";%',
        '%"' . $city_id . '"%'
        );
    }
    
    $types_with_counts = $wpdb->get_results("
        SELECT
            t.term_id,
            t.name,
            t.slug,
            COUNT(vp.ID) as professional_count
        FROM {$wpdb->terms} t
        INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id AND tt.taxonomy = 'professional_type'
        INNER JOIN {$wpdb->term_relationships} tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
        INNER JOIN {$wpdb->posts} vp ON tr.object_id = vp.ID AND vp.post_type = 'vehicle_professional' AND vp.post_status = 'publish'
        WHERE 1=1 {$city_filter}
        GROUP BY t.term_id, t.name, t.slug
        HAVING professional_count > 0
        ORDER BY t.name ASC
    ");
    
    return $types_with_counts;
}

// Get filter parameters and set defaults to prevent PHP warnings
$selected_city = isset($_GET['city']) ? intval($_GET['city']) : '';
$selected_type = isset($_GET['type']) ? sanitize_text_field($_GET['type']) : '';
$search_query = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

// Get cities with professional counts
$cities_with_counts = get_cities_with_professional_counts();
if (!is_array($cities_with_counts)) {
    $cities_with_counts = [];
}

// Get professional types with counts (filtered by city if selected)
$types_with_counts = get_professional_types_with_counts($selected_city);
if (!is_array($types_with_counts)) {
    $types_with_counts = [];
}

// Build query args for professionals
$query_args = [
    'post_type' => 'vehicle_professional',
    'post_status' => 'publish',
    'posts_per_page' => 12,
    'paged' => get_query_var('paged') ? get_query_var('paged') : 1,
];

// Add meta query for city filtering
$meta_query = [];
if ($selected_city) {
    $city_id = intval($selected_city);
    $city_id_str = (string)$city_id;
    
    // Handle the serialized array format of city_town values
    $meta_query[] = [
        'relation' => 'OR',
        // Direct match
        [
            'key' => 'city_town',
            'value' => $city_id_str,
            'compare' => '='
        ],
        // Serialized array format (exact pattern)
        [
            'key' => 'city_town',
            'value' => 'a:1:{i:0;s:' . strlen($city_id_str) . ':"' . $city_id_str . '";}',
            'compare' => '='
        ],
        // Serialized array format (LIKE pattern)
        [
            'key' => 'city_town',
            'value' => 'a:1:{i:0;s:' . strlen($city_id_str) . ':"' . $city_id_str . '";',
            'compare' => 'LIKE'
        ],
        // Any serialized format containing the ID
        [
            'key' => 'city_town',
            'value' => '"' . $city_id_str . '"',
            'compare' => 'LIKE'
        ]
    ];
}

if (!empty($meta_query)) {
    $query_args['meta_query'] = $meta_query;
}

// Add taxonomy query for professional type filtering
if ($selected_type) {
    $query_args['tax_query'] = [
        [
            'taxonomy' => 'professional_type',
            'field' => 'slug',
            'terms' => $selected_type,
        ]
    ];
}

// Add search query
if ($search_query) {
    $query_args['s'] = $search_query;
}

// Make sure we have a valid query
try {
    $professionals_query = new WP_Query($query_args);
} catch (Exception $e) {
    // Log the error
    error_log('Error in vehicle professionals query: ' . $e->getMessage());
    // Create an empty query as fallback
    $professionals_query = new WP_Query([
        'post_type' => 'vehicle_professional',
        'posts_per_page' => 1,
        'post__in' => [0] // This ensures no results
    ]);
}

// Get total count for "All" options
$count_posts = wp_count_posts('vehicle_professional');
$total_professionals = is_object($count_posts) ? $count_posts->publish : 0;
?>

<div class="bg-primary text-white py-12">
    <div class="container-custom">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-serif font-bold mb-4">
                <?php esc_html_e('Vehicle Professionals', 'autohubzambia'); ?>
            </h1>
            <p class="text-xl opacity-90 max-w-2xl mx-auto">
                <?php esc_html_e('Connect with skilled automotive professionals across Zambia', 'autohubzambia'); ?>
            </p>
        </div>
    </div>
</div>

<main class="max-w-7xl mx-auto px-4 py-4">
    <?php
    while (have_posts()) :
        the_post();
        ?>
        <div class="prose prose-lg max-w-none mb-6">
            <?php the_content(); ?>
        </div>
        <?php
    endwhile;
    ?>

    <!-- Search Bar -->
    <div class="bg-white rounded-lg shadow-lg p-5 mb-4">
        <form method="GET" class="space-y-2">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <label for="search" class="block text-xs font-medium text-gray-700 mb-1">Search Professionals</label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="<?php echo esc_attr($search_query); ?>"
                           placeholder="Search by name, skills, or specialization..."
                           class="w-full px-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                </div>
                <div class="flex items-end">
                    <button type="submit" 
                            class="px-4 py-1.5 bg-primary text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 flex items-center text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z"></path>
                        </svg>
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-lg p-5 mb-4">
        <div class="mb-3">
            <h3 class="text-base font-semibold text-gray-900">Filter Professionals</h3>
        </div>

        <div class="space-y-3">
            <form method="GET" id="filter-form">
                <?php if ($search_query): ?>
                    <input type="hidden" name="search" value="<?php echo esc_attr($search_query); ?>">
                <?php endif; ?>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- City Filter -->
                    <div>
                        <label for="city" class="block text-xs font-medium text-gray-700 mb-1">Location</label>
                        <div class="relative">
                            <select id="city" 
                                    name="city" 
                                    class="w-full px-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                                <option value="">All Cities</option>
                                <?php if (!empty($cities_with_counts)): ?>
                                    <?php foreach ($cities_with_counts as $city): ?>
                                        <option value="<?php echo esc_attr($city->ID); ?>" 
                                                <?php selected($selected_city, $city->ID); ?>>
                                            <?php echo esc_html($city->city_name); ?> (<?php echo $city->professional_count; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none text-xs text-gray-500">
                                <span class="filter-hint text-xs">Select</span>
                            </div>
                        </div>
                    </div>

                    <!-- Professional Type Filter -->
                    <div>
                        <label for="type" class="block text-xs font-medium text-gray-700 mb-1">Professional Type</label>
                        <div class="relative">
                            <select id="type" 
                                    name="type" 
                                    class="w-full px-3 py-1.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                                <?php 
                                // Calculate total for "All Types" based on current city filter
                                $all_types_count = 0;
                                if ($selected_city && !empty($types_with_counts)) {
                                    foreach ($types_with_counts as $type) {
                                        if (isset($type->professional_count)) {
                                            $all_types_count += $type->professional_count;
                                        }
                                    }
                                } else {
                                    $all_types_count = $total_professionals;
                                }
                                ?>
                                <option value="">All Types (<?php echo $all_types_count; ?>)</option>
                                <?php if (!empty($types_with_counts)): ?>
                                    <?php foreach ($types_with_counts as $type): ?>
                                        <option value="<?php echo esc_attr($type->slug); ?>" 
                                                <?php selected($selected_type, $type->slug); ?>>
                                            <?php echo esc_html($type->name); ?> (<?php echo $type->professional_count; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none text-xs text-gray-500">
                                <span class="filter-hint text-xs">Select</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-between items-center pt-3">
                    <button type="submit" id="apply-filters-btn"
                            class="px-4 py-1.5 bg-primary text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 flex items-center text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Apply Filters
                    </button>
                    <a href="<?php echo get_permalink(); ?>" 
                       class="text-gray-600 hover:text-gray-800 text-sm">
                        Clear All Filters
                    </a>
                </div>
            </form>
        </div>
        
        <!-- Add JavaScript for auto-submit functionality -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the filter form elements
            const filterForm = document.getElementById('filter-form');
            const citySelect = document.getElementById('city');
            const typeSelect = document.getElementById('type');
            const applyButton = document.getElementById('apply-filters-btn');
            
            // Add visual cue when filters are changed
            function highlightApplyButton() {
                applyButton.classList.add('animate-pulse', 'bg-secondary');
                applyButton.classList.remove('bg-primary');
                
                // Show a tooltip or message
                const tooltip = document.createElement('div');
                tooltip.className = 'absolute -top-10 left-0 right-0 text-center text-sm bg-secondary text-white py-1 px-2 rounded';
                tooltip.textContent = 'Click "Apply Filters" to see results';
                tooltip.style.zIndex = '10';
                
                // Position the tooltip relative to the button
                applyButton.parentNode.style.position = 'relative';
                applyButton.parentNode.appendChild(tooltip);
                
                // Remove the tooltip after 3 seconds
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, 3000);
            }
            
            // Add event listeners to the select elements
            citySelect.addEventListener('change', highlightApplyButton);
            typeSelect.addEventListener('change', highlightApplyButton);
            
            // Optional: Add auto-submit after a delay
            // Uncomment this section to enable auto-submit
            /*
            let submitTimer;
            
            function scheduleSubmit() {
                clearTimeout(submitTimer);
                submitTimer = setTimeout(() => {
                    filterForm.submit();
                }, 1500); // Submit after 1.5 seconds of inactivity
            }
            
            citySelect.addEventListener('change', scheduleSubmit);
            typeSelect.addEventListener('change', scheduleSubmit);
            */
        });
        </script>
    </div>

    <!-- Results -->
    <?php if (isset($professionals_query) && $professionals_query->have_posts()): ?>
        <div class="mb-4">
            <p class="text-gray-600">
                Showing <?php echo $professionals_query->found_posts; ?> professional<?php echo $professionals_query->found_posts !== 1 ? 's' : ''; ?>
                <?php 
                $has_filters = $selected_city || $selected_type || $search_query;
                
                if ($has_filters): 
                ?>
                    matching your criteria
                    <?php if ($selected_city): ?>
                        <?php 
                        $selected_city_name = '';
                        if (!empty($cities_with_counts)) {
                            foreach ($cities_with_counts as $city) {
                                if ($city->ID == $selected_city) {
                                    $selected_city_name = $city->city_name;
                                    break;
                                }
                            }
                        }
                        ?>
                        <?php if ($selected_city_name): ?>
                            in <?php echo esc_html($selected_city_name); ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if ($selected_type): ?>
                        <?php 
                        $selected_type_name = '';
                        if (!empty($types_with_counts)) {
                            foreach ($types_with_counts as $type) {
                                if ($type->slug == $selected_type) {
                                    $selected_type_name = $type->name;
                                    break;
                                }
                            }
                        }
                        ?>
                        <?php if ($selected_type_name): ?>
                            of type "<?php echo esc_html($selected_type_name); ?>"
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <?php while ($professionals_query->have_posts()): $professionals_query->the_post(); ?>
                <article class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    
                    <!-- Profile Photo -->
                    <?php
                    $profile_image = get_post_meta(get_the_ID(), 'profile_image', true);
                    if ($profile_image || has_post_thumbnail()) : ?>
                        <div class="relative h-40 overflow-hidden">
                            <a href="<?php the_permalink(); ?>">
                                <?php if ($profile_image) : ?>
                                    <img src="<?php echo esc_url(wp_get_attachment_image_url($profile_image, 'autohub-featured')); ?>"
                                         alt="<?php the_title(); ?>"
                                         class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                                <?php else : ?>
                                    <?php the_post_thumbnail('autohub-featured', array('class' => 'w-full h-full object-cover hover:scale-105 transition-transform duration-300')); ?>
                                <?php endif; ?>
                            </a>
                        </div>
                    <?php endif; ?>

                    <div class="p-4">
                        <!-- Name -->
                        <h2 class="text-lg font-semibold mb-2">
                            <a href="<?php the_permalink(); ?>" class="text-gray-900 hover:text-primary transition-colors duration-200">
                                <?php
                                // Use the post title as the professional name
                                echo get_the_title();
                                ?>
                            </a>
                        </h2>

                        <!-- Professional Info -->
                        <div class="space-y-1 mb-3">
                            <?php
                            // Get professional type from taxonomy
                            $professional_types = get_the_terms(get_the_ID(), 'professional_type');
                            $services_offered = get_post_meta(get_the_ID(), 'services_offered', true);

                            // Get location information
                            $city_name = '';
                            $province_name = '';

                            // Get city_town and province from post meta directly
                            $city_town = get_post_meta(get_the_ID(), 'city_town', true);
                            $province = get_post_meta(get_the_ID(), 'province', true);
                            
                            // Handle serialized array format
                            if (is_string($city_town) && strpos($city_town, 'a:') === 0) {
                                $city_town = maybe_unserialize($city_town);
                            }
                            
                            if (is_string($province) && strpos($province, 'a:') === 0) {
                                $province = maybe_unserialize($province);
                            }
                            
                            // Extract city ID from array if needed
                            $city_id = null;
                            if (is_array($city_town) && !empty($city_town)) {
                                $city_id = reset($city_town); // Get first element
                            } elseif (is_numeric($city_town)) {
                                $city_id = $city_town;
                            }
                            
                            // Extract province ID from array if needed
                            $province_id = null;
                            if (is_array($province) && !empty($province)) {
                                $province_id = reset($province); // Get first element
                            } elseif (is_numeric($province)) {
                                $province_id = $province;
                            }
                            
                            // Get city name
                            if ($city_id && is_numeric($city_id)) {
                                $city_post = get_post($city_id);
                                if ($city_post && $city_post->post_type === 'city_town') {
                                    $city_name = $city_post->post_title;
                                }
                            }
                            
                            // Get province name
                            if ($province_id && is_numeric($province_id)) {
                                $province_post = get_post($province_id);
                                if ($province_post && $province_post->post_type === 'province') {
                                    $province_name = $province_post->post_title;
                                }
                            }
                            
                            // Fallback to direct meta values if needed
                            if (empty($city_name)) {
                                $city_name = get_post_meta(get_the_ID(), 'city', true) ?: get_post_meta(get_the_ID(), 'city_town', true);
                            }
                            if (empty($province_name)) {
                                $province_name = get_post_meta(get_the_ID(), 'province', true);
                            }
                            
                            $service_rate = get_post_meta(get_the_ID(), 'service_rate', true);
                            $experience_years = get_post_meta(get_the_ID(), 'experience_years', true);
                            $address = get_post_meta(get_the_ID(), 'address', true);
                            $operating_hours = get_post_meta(get_the_ID(), 'operating_hours', true);
                            ?>
                            
                            <?php if ($professional_types && !is_wp_error($professional_types)): ?>
                                <div class="mb-1">
                                    <?php foreach ($professional_types as $type): ?>
                                        <span class="bg-primary-100 text-primary-800 px-2 py-0.5 rounded text-xs font-medium">
                                            <?php echo esc_html($type->name); ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($services_offered && is_array($services_offered)) : ?>
                                <div class="flex flex-wrap gap-1 mb-1">
                                    <?php
                                    $displayed_services = 0;
                                    foreach ($services_offered as $service) :
                                        if ($displayed_services >= 2) break;

                                        // Handle both possible sub-field names
                                        $service_text = '';
                                        if (isset($service['service_name']) && !empty($service['service_name'])) {
                                            $service_text = $service['service_name'];
                                        } elseif (isset($service['service_item']) && !empty($service['service_item'])) {
                                            $service_text = $service['service_item'];
                                        }

                                        if (!empty($service_text)) : ?>
                                            <span class="bg-secondary-100 text-secondary-800 px-2 py-0.5 rounded text-xs font-medium">
                                                <?php echo esc_html($service_text); ?>
                                            </span>
                                            <?php $displayed_services++; ?>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                    <?php if (count($services_offered) > 2) : ?>
                                        <span class="text-xs text-gray-500">+<?php echo count($services_offered) - 2; ?> more</span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="text-xs text-gray-600 flex items-center mb-1">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <?php
                                // Make sure we're not trying to display arrays
                                if (is_array($city_name)) {
                                    $city_name = '';
                                }
                                if (is_array($province_name)) {
                                    $province_name = '';
                                }

                                // Display location information
                                if (!empty($city_name) && !empty($province_name)) {
                                    echo esc_html($city_name) . ', ' . esc_html($province_name);
                                } elseif (!empty($city_name)) {
                                    echo esc_html($city_name);
                                } elseif (!empty($province_name)) {
                                    echo esc_html($province_name);
                                } else {
                                    echo 'Location not specified';
                                }
                                ?>
                            </div>

                            <?php if (!empty($address)) : ?>
                                <div class="text-xs text-gray-600 flex items-center mb-1">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <?php echo esc_html($address); ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($operating_hours) && is_array($operating_hours)) : ?>
                                <div class="text-xs text-gray-600 flex items-center mb-1">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <?php
                                    // Display first operating hours entry
                                    $first_hours = reset($operating_hours);
                                    if ($first_hours && isset($first_hours['from_day'])) {
                                        if (isset($first_hours['closed_all_day']) && $first_hours['closed_all_day']) {
                                            echo 'Closed ' . esc_html($first_hours['from_day']);
                                            if (!empty($first_hours['to_day'])) {
                                                echo '-' . esc_html($first_hours['to_day']);
                                            }
                                        } else {
                                            echo esc_html($first_hours['from_day']);
                                            if (!empty($first_hours['to_day'])) {
                                                echo '-' . esc_html($first_hours['to_day']);
                                            }
                                            if (!empty($first_hours['open_time']) && !empty($first_hours['close_time'])) {
                                                echo ': ' . esc_html($first_hours['open_time']) . '-' . esc_html($first_hours['close_time']);
                                            }
                                        }
                                        if (count($operating_hours) > 1) {
                                            echo ' (+' . (count($operating_hours) - 1) . ' more)';
                                        }
                                    }
                                    ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($experience_years) : ?>
                                <div class="text-xs text-gray-600 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <?php echo esc_html($experience_years); ?> years experience
                                </div>
                            <?php endif; ?>

                            <?php if ($service_rate) : ?>
                                <div class="text-xs text-gray-600 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    ZMW <?php echo esc_html($service_rate); ?>/hour
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Short Bio -->
                        <?php 
                        $short_bio = get_post_meta(get_the_ID(), 'short_bio', true);
                        if ($short_bio) : ?>
                            <div class="text-gray-600 text-xs mb-3">
                                <?php echo esc_html(wp_trim_words($short_bio, 12, '...')); ?>
                            </div>
                        <?php else : ?>
                            <div class="text-gray-600 text-xs mb-3">
                                <?php echo wp_trim_words(get_the_excerpt(), 12, '...'); ?>
                            </div>
                        <?php endif; ?>

                        <!-- Contact/View Details Button -->
                        <div class="flex space-x-2">
                            <a href="<?php the_permalink(); ?>" class="inline-block bg-primary text-white px-3 py-1.5 rounded text-sm hover:bg-primary-dark transition-colors duration-200">
                                View Profile
                            </a>
                            <?php
                            $phone_number = get_post_meta(get_the_ID(), 'phone_number', true);
                            if ($phone_number) : ?>
                                <a href="tel:<?php echo esc_attr($phone_number); ?>" class="inline-block bg-secondary text-white px-3 py-1.5 rounded text-sm hover:bg-secondary-dark transition-colors duration-200">
                                    Call
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </article>
            <?php endwhile; ?>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            <?php
            // Custom pagination for WP_Query
            $big = 999999999; // need an unlikely integer
            echo paginate_links(array(
                'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                'format' => '?paged=%#%',
                'current' => max(1, get_query_var('paged')),
                'total' => $professionals_query->max_num_pages,
                'prev_text' => '← Previous',
                'next_text' => 'Next →',
                'type' => 'list',
                'end_size' => 3,
                'mid_size' => 3
            ));
            ?>
        </div>

    <?php else: ?>
        <div class="text-center py-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">No professionals found</h2>
            <p class="text-gray-600 mb-4">
                <?php if ($selected_city || $selected_type || $search_query): ?>
                    No vehicle professionals match your current search criteria. Try adjusting your filters.
                <?php else: ?>
                    No vehicle professionals have been added yet.
                <?php endif; ?>
            </p>
            <?php if ($selected_city || $selected_type || $search_query): ?>
                <a href="<?php echo get_permalink(); ?>" class="bg-primary text-white px-5 py-2 rounded-lg hover:bg-primary-dark transition-colors duration-200 mr-3 text-sm">
                    Clear Filters
                </a>
            <?php endif; ?>
            <a href="<?php echo home_url(); ?>" class="bg-gray-500 text-white px-5 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200 text-sm">
                Return Home
            </a>
        </div>
    <?php endif; ?>
    
    <?php wp_reset_postdata(); ?>
</main>

<?php get_footer(); ?>