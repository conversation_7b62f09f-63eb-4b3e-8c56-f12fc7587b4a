{"version": 3, "sources": ["location-selector.js", "main.js", "user-registration-admin.js", "user-registration.js"], "names": ["document", "addEventListener", "querySelectorAll", "for<PERSON>ach", "provinceSelect", "citySelectId", "getAttribute", "citySelect", "getElementById", "formData", "provinceId", "this", "value", "innerHTML", "disabled", "FormData", "append", "fetch", "autohub_ajax", "ajax_url", "method", "body", "then", "response", "json", "data", "success", "city", "option", "createElement", "id", "textContent", "title", "append<PERSON><PERSON><PERSON>", "console", "error", "catch", "warn", "Alpine", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedCity", "cities", "loadingCities", "loadCities", "await", "$", "initializeTheme", "initMobileMenu", "mobileMenuButton", "mobileMenu", "on", "e", "preventDefault", "toggleClass", "target", "closest", "length", "removeClass", "animate", "scrollTop", "offset", "top", "initBackToTop", "backToTop", "window", "addClass", "form", "let", "<PERSON><PERSON><PERSON><PERSON>", "find", "each", "field", "val", "trim", "hideFieldError", "showFieldError", "attr", "emailRegex", "test", "hasClass", "message", "errorDiv", "after", "next", "remove", "debounce", "func", "wait", "immediate", "timeout", "context", "args", "arguments", "callNow", "clearTimeout", "setTimeout", "apply", "ready", "AutoHubTheme", "j<PERSON><PERSON><PERSON>", "addVehicleForm", "showNotification", "type", "notification", "className", "classList", "add", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "submitButton", "querySelector", "originalText", "modalElement", "reset", "_x_dataStack", "showAddVehicleModal", "location", "reload", "finally", "showAdminMessage", "$notice", "fadeOut", "isChecked", "is", "ajax", "url", "autohub_admin_ajax", "action", "auto_approve", "nonce", "prop", "userId", "$button", "$row", "text", "user_id", "confirm", "userEmail", "$statusCell", "email", "Date", "html", "$checkboxes", "processBulkResendVerification", "processed", "total", "patterns", "username", "phone", "password", "<PERSON><PERSON><PERSON><PERSON>", "hasLowercase", "hasUppercase", "hasNumber", "hasSpecial", "messages", "required", "pattern", "exists", "invalid", "weak", "confirmPassword", "mismatch", "businessName", "firstName", "lastName", "corporateName", "showFieldFeedback", "$field", "$feedback", "siblings", "clearFieldFeedback", "validateUsername", "callback", "autohub_reg_ajax", "available", "validateEmail", "validatePhone", "cleanPhone", "replace", "valid", "validatePassword", "strength", "score", "missing", "push", "join", "slice", "validateConfirmPassword", "validateRequiredText", "fieldName", "validateFormBeforeSubmit", "$form", "errors", "label", "$passwordField", "$confirmPasswordField", "passwordValidation", "confirmValidation", "initConsumerRegistration", "filter", "consumerType", "$individualFields", "$corporateFields", "removeAttr", "trigger", "debouncedUsernameCheck", "debouncedEmail<PERSON><PERSON>ck", "result", "initBusinessRegistration", "$passwordFields", "$strengthIndicator", "$strengthText", "$strengthBars", "char<PERSON>t", "toUpperCase", "eq", "confirmResult", "confirmField", "fieldId", "targetId", "$input", "$eyeOpen", "$eyeClosed", "$btn", "$messageDiv", "show", "hide", "xhr", "status", "responseText", "$container", "$statusDisplay", "urlParams", "URLSearchParams", "search", "get", "token", "showSuccess", "showMessage", "showError", "$messagesDiv", "isValidEmail", "displayStatus", "statusData", "$actionButtons", "statusHtml", "statusClass", "can_resend", "verification_attempts", "newEmail", "old_email", "new_email", "newUrl", "URL", "searchParams", "set", "history", "replaceState", "enteredEmail", "href", "toString", "loadPlanCosts", "$planCostDisplay", "$registrationTypeInputs", "planCosts", "showPlanCost", "planPricingContent", "<PERSON><PERSON><PERSON>", "$selectedInput", "checked", "hasUsername", "hasEmail", "hasPassword", "$submitBtn", "$submitText", "$loadingText", "validation", "processData", "contentType", "scrollIntoView", "behavior", "block", "errorMessage", "complete", "errorHtml", "$firstError", "first", "focus", "$submissionForms", "formId"], "mappings": "AAKAA,SAAAC,iBAAA,mBAAA,WAEAD,SAAAE,iBAAA,0BAAA,EAEAC,QAAA,SAAAC,GACA,IAAAC,EAAAD,EAAAE,aAAA,wBAAA,EACA,IAAAC,EAAAP,SAAAQ,eAAAH,CAAA,EAEAE,EAKAH,EAAAH,iBAAA,SAAA,WACA,IAcAQ,EAdAC,EAAAC,KAAAC,MAGAL,EAAAM,UAAA,gDACAN,EAAAO,SAAA,CAAA,EAEAJ,IAKAH,EAAAM,UAAA,+CAGAJ,EAAA,IAAAM,UACAC,OAAA,SAAA,wBAAA,EACAP,EAAAO,OAAA,cAAAN,CAAA,EAEAO,MAAAC,aAAAC,SAAA,CACAC,OAAA,OACAC,KAAAZ,CACA,CAAA,EACAa,KAAAC,GAAAA,EAAAC,KAAA,CAAA,EACAF,KAAAG,IACAA,EAAAC,SAEAnB,EAAAM,UAAA,gDAGAY,EAAAA,KAAAtB,QAAA,SAAAwB,GACA,IAAAC,EAAA5B,SAAA6B,cAAA,QAAA,EACAD,EAAAhB,MAAAe,EAAAG,GACAF,EAAAG,YAAAJ,EAAAK,MACAzB,EAAA0B,YAAAL,CAAA,CACA,CAAA,EAEArB,EAAAO,SAAA,CAAA,IAEAP,EAAAM,UAAA,iDACAqB,QAAAC,MAAA,wBAAAV,CAAA,EAEA,CAAA,EACAW,MAAAD,IACA5B,EAAAM,UAAA,iDACAqB,QAAAC,MAAA,cAAAA,CAAA,CACA,CAAA,EACA,CAAA,EAnDAD,QAAAG,KAAA,iCAAAhC,CAAA,CAoDA,CAAA,CACA,CAAA,EAKA,aAAA,OAAAiC,QACAA,OAAAb,KAAA,mBAAA,KAAA,CACAc,iBAAA,GACAC,aAAA,GACAC,OAAA,GACAC,cAAA,CAAA,EAEAC,mBACA,GAAAhC,KAAA4B,iBAAA,CAMA5B,KAAA+B,cAAA,CAAA,EACA/B,KAAA6B,aAAA,GAEA,IACA,IAAA/B,EAAA,IAAAM,SACAN,EAAAO,OAAA,SAAA,wBAAA,EACAP,EAAAO,OAAA,cAAAL,KAAA4B,gBAAA,EAFA,IASAd,EAAAmB,MALAA,MAAA3B,MAAAC,aAAAC,SAAA,CACAC,OAAA,OACAC,KAAAZ,CACA,CAAA,GAEAe,KAAA,EAEAC,EAAAC,QACAf,KAAA8B,OAAAhB,EAAAA,MAEAS,QAAAC,MAAA,wBAAAV,CAAA,EACAd,KAAA8B,OAAA,GAOA,CALA,MAAAN,GACAD,QAAAC,MAAA,cAAAA,CAAA,EACAxB,KAAA8B,OAAA,EACA,CAAA,QACA9B,KAAA+B,cAAA,CAAA,CACA,CA5BA,MAHA/B,KAAA8B,OAAA,GACA9B,KAAA6B,aAAA,EA+BA,CACA,EAAA,GA7GAK,IASA,SAAAC,IACAC,CAQA,IAAAC,EAAAH,EAAA,qBAAA,EACAI,EAAAJ,EAAA,cAAA,EAEAG,EAAAE,GAAA,QAAA,SAAAC,GACAA,EAAAC,eAAA,EACAP,EAAAlC,IAAA,EAAA0C,YAAA,QAAA,EACAJ,EAAAI,YAAA,QAAA,EACAR,EAAA,MAAA,EAAAQ,YAAA,WAAA,CACA,CAAA,EAGAR,EAAA7C,QAAA,EAAAkD,GAAA,QAAA,SAAAC,GACAN,EAAAM,EAAAG,MAAA,EAAAC,QAAA,mCAAA,EAAAC,SACAR,EAAAS,YAAA,QAAA,EACAR,EAAAQ,YAAA,QAAA,EACAZ,EAAA,MAAA,EAAAY,YAAA,WAAA,EAEA,CAAA,CAzBA,CA8BAZ,EAAA,8BAAA,EAAAK,GAAA,QAAA,SAAAC,GACA,IAAAG,EAAAT,EAAAlC,KAAAL,aAAA,MAAA,CAAA,EACAgD,EAAAE,SACAL,EAAAC,eAAA,EACAP,EAAA,YAAA,EAAAa,QAAA,CACAC,UAAAL,EAAAM,OAAA,EAAAC,IAAA,EACA,EAAA,GAAA,EAEA,CAAA,EApCAC,CAyCA,IAAAC,EAAAlB,EAAA,cAAA,EAEAkB,EAAAP,SACAX,EAAAmB,MAAA,EAAAd,GAAA,SAAA,WACA,IAAAL,EAAAlC,IAAA,EAAAgD,UAAA,EACAI,EAAAE,SAAA,SAAA,EAEAF,EAAAN,YAAA,SAAA,CAEA,CAAA,EAEAM,EAAAb,GAAA,QAAA,SAAAC,GACAA,EAAAC,eAAA,EACAP,EAAA,YAAA,EAAAa,QAAA,CACAC,UAAA,CACA,EAAA,GAAA,CACA,CAAA,EAzDA,CA+DAd,EAAA,MAAA,EAAAK,GAAA,SAAA,SAAAC,GACA,IAAAe,EAAArB,EAAAlC,IAAA,EACAwD,IAAAC,EAAA,CAAA,EAGAF,EAAAG,KAAA,YAAA,EAAAC,KAAA,WACA,IAAAC,EAAA1B,EAAAlC,IAAA,EACAC,EAAA2D,EAAAC,IAAA,EAAAC,KAAA,EAEA7D,GAKA2D,EAAAd,YAAA,OAAA,EACAiB,EAAAH,CAAA,IALAH,EAAA,CAAA,EACAG,EAAAN,SAAA,OAAA,EACAU,EAAAJ,EAAA,yBAAA,GAOA,UAAAA,EAAAK,KAAA,MAAA,GAAAhE,GAEAiE,CADA,6BACAC,KAAAlE,CAAA,IACAwD,EAAA,CAAA,EACAG,EAAAN,SAAA,OAAA,EACAU,EAAAJ,EAAA,qCAAA,EAGA,CAAA,EAEAH,GACAjB,EAAAC,eAAA,CAEA,CAAA,EAGAP,EAAA,yBAAA,EAAAK,GAAA,eAAA,WCnHA,IAAAqB,EAAA1B,EAAAlC,IAAA,EACA4D,EAAAQ,SAAA,OAAA,IACAR,EAAAd,YAAA,OAAA,EACAiB,EAAAH,CAAA,EAEA,CAAA,CDcA,CCVA,SAAAI,EAAAJ,EAAAS,GACAN,EAAAH,CAAA,EACAU,EAAApC,EAAA,sDAAAmC,EAAA,QAAA,EACAT,EAAAW,MAAAD,CAAA,CACA,CAGA,SAAAP,EAAAH,GACAA,EAAAY,KAAA,cAAA,EAAAC,OAAA,CACA,CAGA,SAAAC,EAAAC,EAAAC,EAAAC,GACArB,IAAAsB,EACA,OAAA,WACA,IAAAC,EAAA/E,KACAgF,EAAAC,UACA,IAIAC,EAAAL,GAAA,CAAAC,EACAK,aAAAL,CAAA,EACAA,EAAAM,WANA,WACAN,EAAA,KACAD,GAAAF,EAAAU,MAAAN,EAAAC,CAAA,CACA,EAGAJ,CAAA,EACAM,GAAAP,EAAAU,MAAAN,EAAAC,CAAA,CACA,CACA,CD1BA9C,EAAA7C,QAAA,EAAAiG,MAAA,WACAnD,EAAA,CACA,CAAA,EC2BAD,EAAAmB,MAAA,EAAAd,GAAA,SAAAmC,EAAA,aAEA,GAAA,CAAA,EAGArB,OAAAkC,aAAA,CACApD,gBAAAA,EACAuC,SAAAA,CACA,CAEA,GAAAc,MAAA,EAMAnG,SAAAC,iBAAA,mBAAA,WAEA,IAAAmG,EAAApG,SAAAQ,eAAA,kBAAA,EA4DA,SAAA6F,EAAAC,EAAAtB,GAEA,IAAAuB,EAAAvG,SAAA6B,cAAA,KAAA,EACA0E,EAAAC,UAAA,sHACA,YAAAF,EAAA,0BAAA,yBAEAC,EAAA1F;;;sBAGA,YAAAyF,EACA,mGACA;;wBAGAtB;;UAIAhF,SAAAqB,KAAAY,YAAAsE,CAAA,EAGAR,WAAA,KACAQ,EAAAE,UAAArB,OAAA,kBAAA,CACA,EAAA,GAAA,EAGAW,WAAA,KACAQ,EAAAE,UAAAC,IAAA,kBAAA,EACAX,WAAA,KACAQ,EAAAI,YACAJ,EAAAI,WAAAC,YAAAL,CAAA,CAEA,EAAA,GAAA,CACA,EAAA,GAAA,CACA,CA5FAH,GACAA,EAAAnG,iBAAA,SAAA,SAAAkD,GACAA,EAAAC,eAAA,EAEA,IAAAyD,EAAAlG,KAAAmG,cAAA,uBAAA,EACAC,EAAAF,EAAA9E,YAGA8E,EAAA/F,SAAA,CAAA,EACA+F,EAAAhG,UAAA,saAGAJ,EAAA,IAAAM,SAAAJ,IAAA,EACAF,EAAAO,OAAA,SAAA,aAAA,EACAP,EAAAO,OAAA,QAAAhB,SAAA8G,cAAA,oBAAA,EAAAlG,KAAA,EAGAK,MAAAC,aAAAC,SAAA,CACAC,OAAA,OACAC,KAAAZ,CACA,CAAA,EACAa,KAAAC,GAAAA,EAAAC,KAAA,CAAA,EACAF,KAAAG,IACA,IAQAuF,EARAvF,EAAAC,SAEA2E,EAAA,UAAA5E,EAAAA,KAAAuD,OAAA,EAGAoB,EAAAa,MAAA,GAGAD,EAAAhH,SAAA8G,cAAA,UAAA,IACAE,EAAAE,eACAF,EAAAE,aAAA,GAAAC,oBAAA,CAAA,GAIApB,WAAA,KACA/B,OAAAoD,SAAAC,OAAA,CACA,EAAA,IAAA,GAGAhB,EAAA,QAAA5E,EAAAA,MAAA,6CAAA,CAEA,CAAA,EACAW,MAAAD,IACAD,QAAAC,MAAA,SAAAA,CAAA,EACAkE,EAAA,QAAA,6CAAA,CACA,CAAA,EACAiB,QAAA,KAEAT,EAAA/F,SAAA,CAAA,EACA+F,EAAA9E,YAAAgF,CACA,CAAA,CACA,CAAA,CAuCA,CAAA,GClJAlE,IAGAA,EAAA7C,QAAA,EAAAiG,MAAA,WA0MA,SAAAsB,EAAAvC,EAAAsB,GACA,IAAAkB,EAAA3E,EAAA,6BAAAyD,EAAA,uBAAAtB,EAAA,YAAA,EACAnC,EAAA,UAAA,EAAAqC,MAAAsC,CAAA,EAGAzB,WAAA,WACAyB,EAAAC,QAAA,CACA,EAAA,GAAA,CACA,CA9MA5E,EAAA,sBAAA,EAAAK,GAAA,SAAA,WACA,IAAAwE,EAAA7E,EAAAlC,IAAA,EAAAgH,GAAA,UAAA,EAEA9E,EAAA+E,KAAA,CACAC,IAAAC,mBAAA3G,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,8BACAC,aAAAN,EAAA,MAAA,KACAO,MAAAH,mBAAAG,KACA,EACAvG,QAAA,SAAAH,GACAA,EAAAG,UAGAQ,QAAAC,MAAA,sDAAA,EAEAU,EAAA,sBAAA,EAAAqF,KAAA,UAAA,CAAAR,CAAA,EAEA,EACAvF,MAAA,WACAD,QAAAC,MAAA,oDAAA,EAEAU,EAAA,sBAAA,EAAAqF,KAAA,UAAA,CAAAR,CAAA,CACA,CACA,CAAA,CACA,CAAA,EAGA7E,EAAA,eAAA,EAAAK,GAAA,QAAA,WACA,IAAAiF,EAAAtF,EAAAlC,IAAA,EAAAc,KAAA,SAAA,EACA,IAAA2G,EAAAvF,EAAAlC,IAAA,EACA0H,EAAAD,EAAA7E,QAAA,IAAA,EAEA6E,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,cAAA,EAEAzF,EAAA+E,KAAA,CACAC,IAAAC,mBAAA3G,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,uBACAQ,QAAAJ,EACAF,MAAAH,mBAAAG,KACA,EACAvG,QAAA,SAAAH,GACAA,EAAAG,SACA2G,EAAAZ,QAAA,IAAA,WACA5E,EAAAlC,IAAA,EAAAyE,OAAA,CACA,CAAA,EAGAmC,EAAA,8BAAA,SAAA,IAEAA,EAAAhG,EAAAE,KAAAuD,SAAA,yBAAA,OAAA,EACAoD,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,SAAA,EAEA,EACAnG,MAAA,WACAoF,EAAA,uBAAA,OAAA,EACAa,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,SAAA,CACA,CACA,CAAA,CACA,CAAA,EAGAzF,EAAA,cAAA,EAAAK,GAAA,QAAA,WACA,IAAAiF,EAAAtF,EAAAlC,IAAA,EAAAc,KAAA,SAAA,EACA,IAAA2G,EAAAvF,EAAAlC,IAAA,EACA0H,EAAAD,EAAA7E,QAAA,IAAA,EAEAiF,QAAA,0EAAA,IAIAJ,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,cAAA,EAEAzF,EAAA+E,KAAA,CACAC,IAAAC,mBAAA3G,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,sBACAQ,QAAAJ,EACAF,MAAAH,mBAAAG,KACA,EACAvG,QAAA,SAAAH,GACAA,EAAAG,SACA2G,EAAAZ,QAAA,IAAA,WACA5E,EAAAlC,IAAA,EAAAyE,OAAA,CACA,CAAA,EAGAmC,EAAA,8BAAA,SAAA,IAEAA,EAAAhG,EAAAE,KAAAuD,SAAA,wBAAA,OAAA,EACAoD,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,QAAA,EAEA,EACAnG,MAAA,WACAoF,EAAA,uBAAA,OAAA,EACAa,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,QAAA,CACA,CACA,CAAA,EACA,CAAA,EAGAzF,EAAA,sBAAA,EAAAK,GAAA,QAAA,WACAL,EAAAlC,IAAA,EAAAc,KAAA,SAAA,EAAA,IACAgH,EAAA5F,EAAAlC,IAAA,EAAAc,KAAA,YAAA,EACA,IAAA2G,EAAAvF,EAAAlC,IAAA,EACA+H,EAAAN,EAAA7E,QAAA,IAAA,EAAAc,KAAA,iBAAA,EAEA+D,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,YAAA,EAEAzF,EAAA+E,KAAA,CACAC,IAAAC,mBAAA3G,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,8BACAY,MAAAF,EACAR,MAAAH,mBAAAG,KACA,EACAvG,QAAA,SAAAH,GACAA,EAAAG,SACA6F,EAAAhG,EAAAE,KAAAuD,QAAA,SAAA,EACAoD,EAAAE,KAAA,aAAA,EAGA,IAAAM,KAEAF,EAAAG,KAAA,iGAAA,EAGA9C,WAAA,WACAqC,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,qBAAA,CACA,EAAA,GAAA,IAEAf,EAAAhG,EAAAE,KAAAuD,SAAA,oCAAA,OAAA,EACAoD,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,qBAAA,EAEA,EACAnG,MAAA,WACAoF,EAAA,mCAAA,OAAA,EACAa,EAAAF,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,qBAAA,CACA,CACA,CAAA,CACA,CAAA,EAGAzF,EAAA,yDAAA,EAAAK,GAAA,SAAA,WACA,IAAA6E,EAAAlF,EAAAlC,IAAA,EAAA6D,IAAA,EACAsE,EAAAjG,EAAA,wBAAA,EAEA,GAAA,wBAAAkF,GAAA,EAAAe,EAAAtF,QACAgF,QAAA,+BAAAM,EAAAtF,OAAA,kBAAA,EACAuF,CAMAD,EANAA,EAOA3E,IAAA6E,EAAA,EACAC,EAAAH,EAAAtF,OAEAsF,EAAAxE,KAAA,WACAzB,EAAAlC,IAAA,EAAA6D,IAAA,EAAA,IACAiE,EAAA5F,EAAAlC,IAAA,EAAAc,KAAA,YAAA,EACA,IAAA4G,EAAAxF,EAAAlC,IAAA,EAAA4C,QAAA,IAAA,EAEAV,EAAA+E,KAAA,CACAC,IAAAC,mBAAA3G,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,8BACAY,MAAAF,EACAR,MAAAH,mBAAAG,KACA,EACAvG,QAAA,SAAAH,GACAyH,CAAA,GACAzH,EAAAG,SACA2G,EAAAhE,KAAA,sBAAA,EAAAiE,KAAA,aAAA,EAGAU,IAAAC,GACA1B,EAAA,sCAAA,SAAA,CAEA,EACApF,MAAA,WACA6G,EAAAA,IACAC,GACA1B,EAAA,sDAAA,SAAA,CAEA,CACA,CAAA,CACA,CAAA,CAxCA,CAGA,CAAA,CAkDA,CAAA,CAEA,GAAApB,MAAA,GCxNAtD,IAIA,IAAAqG,EAAA,CACAC,SAAA,mBACAR,MAAA,6BACAS,MAAA,sBACAC,SAAA,CACAC,UAAA,EACAC,aAAA,QACAC,aAAA,QACAC,UAAA,QACAC,WAAA,wBACA,CACA,EAGAC,EAAA,CACAR,SAAA,CACAS,SAAA,uBACAN,UAAA,8CACAO,QAAA,uEACAC,OAAA,gCACA,EACAnB,MAAA,CACAiB,SAAA,4BACAG,QAAA,qCACAD,OAAA,0CACA,EACAV,MAAA,CACAQ,SAAA,2BACAG,QAAA,oEACA,EACAV,SAAA,CACAO,SAAA,uBACAN,UAAA,8CACAU,KAAA,6EACA,EACAC,gBAAA,CACAL,SAAA,+BACAM,SAAA,wBACA,EACAC,aAAA,CACAP,SAAA,4BACAN,UAAA,kDACA,EACAc,UAAA,CACAR,SAAA,yBACAN,UAAA,+CACA,EACAe,SAAA,CACAT,SAAA,wBACAN,UAAA,8CACA,EACAgB,cAAA,CACAV,SAAA,6BACAN,UAAA,mDACA,CACA,EAGA,SAAAjE,EAAAC,EAAAC,GACApB,IAAAsB,EACA,OAAA,YAAAE,GAKAG,aAAAL,CAAA,EACAA,EAAAM,WALA,KACAD,aAAAL,CAAA,EACAH,EAAA,GAAAK,CAAA,CACA,EAEAJ,CAAA,CACA,CACA,CAGA,SAAAgF,EAAAhG,EAAAS,EAAAsB,EAAA,SACAkE,EAAA3H,EAAA0B,CAAA,EAEAJ,IAAAsG,EAAAD,EAAAE,SAAA,iBAAA,EACA,IAAAD,EAAAjH,SACAiH,EAAAD,EAAAjH,QAAA,aAAA,EAAAc,KAAA,iBAAA,GAIAmG,EAAA/G,YAAA,iDAAA,EACAgH,EAAAhH,YAAA,oCAAA,EAEA,UAAA6C,GACAkE,EAAAvG,SAAA,gBAAA,EACAwG,EAAAxG,SAAA,cAAA,EAAAqE,KAAAtD,CAAA,EAAAvB,YAAA,QAAA,GACA,YAAA6C,GACAkE,EAAAvG,SAAA,kBAAA,EACAwG,EAAAxG,SAAA,gBAAA,EAAAqE,KAAAtD,CAAA,EAAAvB,YAAA,QAAA,IAEA+G,EAAAvG,SAAA,iBAAA,EACAwG,EAAAxG,SAAA,QAAA,EAEA,CAGA,SAAA0G,EAAApG,GACA,IAAAiG,EAAA3H,EAAA0B,CAAA,EACAkG,EAAAD,EAAAE,SAAA,iBAAA,EAEAF,EAAA/G,YAAA,iCAAA,EAAAQ,SAAA,iBAAA,EACAwG,EAAAxG,SAAA,QAAA,CACA,CAGA,SAAA2G,EAAAzB,EAAA0B,GACA1B,EAKAA,EAAA3F,OAAA,EACAqH,EAAAlB,EAAAR,SAAAG,UAAA,OAAA,EAIAJ,EAAAC,SAAArE,KAAAqE,CAAA,EAMAtG,EAAA+E,KAAA,CACAC,IAAAiD,iBAAA3J,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,yBACAoB,SAAAA,EACAlB,MAAA6C,iBAAA7C,KACA,EACAvG,QAAA,SAAAH,GACAA,EAAAG,QACAH,EAAAE,KAAAsJ,UACAF,EAAA,wBAAA,SAAA,EAEAA,EAAAlB,EAAAR,SAAAW,OAAA,OAAA,EAGAe,EAAAtJ,EAAAE,KAAAuD,SAAA,0BAAA,OAAA,CAEA,EACA7C,MAAA,WACA0I,EAAA,0BAAA,OAAA,CACA,CACA,CAAA,EA3BAA,EAAAlB,EAAAR,SAAAU,QAAA,OAAA,EAVAgB,EAAAlB,EAAAR,SAAAS,SAAA,OAAA,CAsCA,CAGA,SAAAoB,EAAArC,EAAAkC,GACAlC,EAKAO,EAAAP,MAAA7D,KAAA6D,CAAA,EAMA9F,EAAA+E,KAAA,CACAC,IAAAiD,iBAAA3J,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,sBACAY,MAAAA,EACAV,MAAA6C,iBAAA7C,KACA,EACAvG,QAAA,SAAAH,GACAA,EAAAG,QACAH,EAAAE,KAAAsJ,UACAF,EAAA,qBAAA,SAAA,EAEAA,EAAAlB,EAAAhB,MAAAmB,OAAA,OAAA,EAGAe,EAAAtJ,EAAAE,KAAAuD,SAAA,uBAAA,OAAA,CAEA,EACA7C,MAAA,WACA0I,EAAA,uBAAA,OAAA,CACA,CACA,CAAA,EA3BAA,EAAAlB,EAAAhB,MAAAoB,QAAA,OAAA,EALAc,EAAAlB,EAAAhB,MAAAiB,SAAA,OAAA,CAiCA,CAGA,SAAAqB,EAAA7B,GACA,OAAAA,GAKA8B,EAAA9B,EAAA+B,QAAA,SAAA,EAAA,EAEAjC,EAAAE,MAAAtE,KAAAoG,CAAA,EAIA,CAAAE,MAAA,CAAA,EAAApG,QAAA,oBAAA,EAHA,CAAAoG,MAAA,CAAA,EAAApG,QAAA2E,EAAAP,MAAAW,OAAA,GAPA,CAAAqB,MAAA,CAAA,EAAApG,QAAA2E,EAAAP,MAAAQ,QAAA,CAWA,CAGA,SAAAyB,EAAAhC,GACA,GAAA,CAAAA,EACA,MAAA,CAAA+B,MAAA,CAAA,EAAApG,QAAA2E,EAAAN,SAAAO,SAAA0B,SAAA,MAAA,EAGA,GAAAjC,EAAA7F,OAAA0F,EAAAG,SAAAC,UACA,MAAA,CAAA8B,MAAA,CAAA,EAAApG,QAAA2E,EAAAN,SAAAC,UAAAgC,SAAA,MAAA,EAGAnH,IAAAmH,EAAA,OACAC,EAAA,EACApH,IAAAqH,EAAA,GAgCAJ,GA7BAlC,EAAAG,SAAAE,aAAAzE,KAAAuE,CAAA,EACAkC,CAAA,GAEAC,EAAAC,KAAA,kBAAA,EAGAvC,EAAAG,SAAAG,aAAA1E,KAAAuE,CAAA,EACAkC,CAAA,GAEAC,EAAAC,KAAA,kBAAA,EAGAvC,EAAAG,SAAAI,UAAA3E,KAAAuE,CAAA,EACAkC,CAAA,GAEAC,EAAAC,KAAA,QAAA,EAGAvC,EAAAG,SAAAK,WAAA5E,KAAAuE,CAAA,EACAkC,CAAA,GAEAC,EAAAC,KAAA,mBAAA,EAIA,GAAAF,EAAAD,EAAA,SACA,GAAAC,IAAAD,EAAA,UAGA,GAAAC,GAEApH,IAAAa,EAaA,MAAA,CAAAoG,MAAAA,EAAApG,QAXAA,EADAoG,EACA,sBAAAE,EAEA,IAAAE,EAAAhI,OACA,mBAAAgI,EAAA,GACA,IAAAA,EAAAhI,OACA,mBAAAgI,EAAAE,KAAA,OAAA,qBAEAF,EAAAG,MAAA,EAAA,CAAA,CAAA,EAAAD,KAAA,IAAA,SAAAF,EAAAA,EAAAhI,OAAA,GAIA8H,SAAAA,CAAA,CACA,CAGA,SAAAM,EAAAvC,EAAAY,GACA,OAAAA,EAIAZ,IAAAY,EACA,CAAAmB,MAAA,CAAA,EAAApG,QAAA2E,EAAAM,gBAAAC,QAAA,EAGA,CAAAkB,MAAA,CAAA,EAAApG,QAAA,iBAAA,EAPA,CAAAoG,MAAA,CAAA,EAAApG,QAAA2E,EAAAM,gBAAAL,QAAA,CAQA,CAGA,SAAAiC,EAAAjL,EAAAkL,EAAAxC,EAAA,GAEA,OAAAK,EAAAmC,GAIAlL,GAAA,KAAAA,EAAA6D,KAAA,EAIA7D,EAAA6D,KAAA,EAAAjB,OAAA8F,EACA,CAAA8B,MAAA,CAAA,EAAApG,QAAA2E,EAAAmC,GAAAxC,SAAA,EAGA,CAAA8B,MAAA,CAAA,EAAApG,QAAA,EAAA,EAPA,CAAAoG,MAAA,CAAA,EAAApG,QAAA2E,EAAAmC,GAAAlC,QAAA,EAJA,CAAAwB,MAAA,CAAA,EAAApG,QAAA,wBAAA,CAYA,CAqVA,SAAA+G,EAAAC,GACA7H,IAAAC,EAAA,CAAA,EACA6H,EAAA,GAGAD,EAAA3H,KAAA,YAAA,EAAAC,KAAA,WACA,IAAAkG,EAAA3H,EAAAlC,IAAA,EACA6J,EAAAhG,IAAA,EAAAC,KAAA,IAGAyH,EAAA1B,EAAAjH,QAAA,aAAA,EAAAc,KAAA,OAAA,EAAAiE,KAAA,EAAA6C,QAAA,IAAA,EAAA,EAAA1G,KAAA,EACAwH,EAAAR,KAAAS,EAAA,cAAA,EACA3B,EAAA5J,KAAA,yBAAA,OAAA,EACAyD,EAAA,CAAA,EAEA,CAAA,EAGA,IAAA+H,EAAAH,EAAA3H,KAAA,wBAAA,EAaA+H,GAZAD,CAAAA,EAAA3I,SAEA6I,EAAAhB,EADAc,EAAA3H,IAAA,CACA,GAEA4G,QACAa,EAAAR,KAAA,qCAAA,EACAlB,EAAA4B,EAAA,GAAAE,EAAArH,QAAA,OAAA,EACAZ,EAAA,CAAA,GAKA4H,EAAA3H,KAAA,gCAAA,GAkBA,OAjBA+H,EAAA5I,QAAA2I,EAAA3I,UAGA8I,EAAAV,EAFAO,EAAA3H,IAAA,EACA4H,EAAA5H,IAAA,CACA,GAEA4G,QACAa,EAAAR,KAAA,sCAAA,EACAlB,EAAA6B,EAAA,GAAAE,EAAAtH,QAAA,OAAA,EACAZ,EAAA,CAAA,IAKA4H,EAAA3H,KAAA,iBAAA,EAAAC,KAAA,WACAF,EAAA,CAAA,CACA,CAAA,EAEA,CAAAA,QAAAA,EAAA6H,OAAAA,CAAA,CACA,CAwIApJ,EAAA7C,QAAA,EAAAiG,MAAA,WAEAsG,CA7gBApI,IAAA6H,EAAAnJ,EAAA,4BAAA,EASA,IALAmJ,EADAA,EAAAxI,OAMAwI,EALAnJ,EAAA,MAAA,EAAA2J,OAAA,WACA,OAAA,EAAA3J,EAAAlC,IAAA,EAAA0D,KAAA,6BAAA,EAAAb,MACA,CAAA,GAGAA,OAAA,CAGAwI,EAAA3H,KAAA,6BAAA,EAAAnB,GAAA,SAAA,WACA,IAAAuJ,EAAA5J,EAAAlC,IAAA,EAAA6D,IAAA,EACAkI,EAAA7J,EAAA,oBAAA,EACA8J,EAAA9J,EAAA,mBAAA,GAEA,cAAA4J,GACAC,EAAAzI,SAAA,QAAA,EACA0I,EAAAlJ,YAAA,QAAA,EAGAkJ,EAAAtI,KAAA,OAAA,EAAA6D,KAAA,WAAA,CAAA,CAAA,EAAAtD,KAAA,WAAA,CAAA,CAAA,EAEA8H,EAAArI,KAAA,OAAA,EAAA6D,KAAA,WAAA,CAAA,CAAA,EAAA0E,WAAA,UAAA,EAGAF,EAAArI,KAAA,OAAA,EAAAZ,YAAA,iCAAA,EAAAQ,SAAA,iBAAA,EACAyI,IAEAA,EAAAjJ,YAAA,QAAA,EACAkJ,EAAA1I,SAAA,QAAA,EAGAyI,EAAArI,KAAA,OAAA,EAAA6D,KAAA,WAAA,CAAA,CAAA,EAAAtD,KAAA,WAAA,CAAA,CAAA,EAEA+H,EAAAtI,KAAA,OAAA,EAAA6D,KAAA,WAAA,CAAA,CAAA,EAAA0E,WAAA,UAAA,EAGAD,EAAAtI,KAAA,OAAA,EAAAZ,YAAA,iCAAA,EAAAQ,SAAA,iBAAA,EACA0I,IAZAtI,KAAA,iBAAA,EAAAJ,SAAA,QAAA,CAcA,CAAA,EAGA+H,EAAA3H,KAAA,qCAAA,EAAAwI,QAAA,QAAA,EAGA,IAAAC,EAAAzH,EAAA,SAAA8D,EAAA5E,GACAqG,EAAAzB,EAAA,SAAAnE,EAAAsB,GACAiE,EAAAhG,EAAAS,EAAAsB,CAAA,CACA,CAAA,CACA,EAAA,GAAA,EAgBAyG,GAZAf,EAAA3H,KAAA,WAAA,EAAAnB,GAAA,aAAA,WACA,IAAAiG,EAAAtG,EAAAlC,IAAA,EAAA6D,IAAA,EAAAC,KAAA,EAGA0E,EACA2D,EAAA3D,EAHAxI,IAGA,EAEAgK,EALAhK,IAKA,CAEA,CAAA,EAGA0E,EAAA,SAAAsD,EAAApE,GACAyG,EAAArC,EAAA,SAAA3D,EAAAsB,GACAiE,EAAAhG,EAAAS,EAAAsB,CAAA,CACA,CAAA,CACA,EAAA,GAAA,GAEA0F,EAAA3H,KAAA,QAAA,EAAAnB,GAAA,aAAA,WACA,IAAAyF,EAAA9F,EAAAlC,IAAA,EAAA6D,IAAA,EAAAC,KAAA,EAGAkE,EACAoE,EAAApE,EAHAhI,IAGA,EAEAgK,EALAhK,IAKA,CAEA,CAAA,EAGAqL,EAAA3H,KAAA,QAAA,EAAAnB,GAAA,aAAA,WACA,IAAAkG,EAAAvG,EAAAlC,IAAA,EAAA6D,IAAA,EAAAC,KAAA,EACAuI,EAAA/B,EAAA7B,CAAA,EAEAA,EACAmB,EAAA5J,KAAAqM,EAAAhI,QAAAgI,EAAA5B,MAAA,UAAA,OAAA,EAEAT,EAAAhK,IAAA,CAEA,CAAA,CAtFA,CAogBA,CACAsM,CAtaA9I,IAAA6H,EAAAnJ,EAAA,uEAAA,EASA,IALAmJ,EADAA,EAAAxI,OAMAwI,EALAnJ,EAAA,MAAA,EAAA2J,OAAA,WACA,OAAA,EAAA3J,EAAAlC,IAAA,EAAA0D,KAAA,0DAAA,EAAAb,MACA,CAAA,GAGAA,OAAA,CAGA,IAAAsJ,EAAAzH,EAAA,SAAA8D,EAAA5E,GACAqG,EAAAzB,EAAA,SAAAnE,EAAAsB,GACAiE,EAAAhG,EAAAS,EAAAsB,CAAA,CACA,CAAA,CACA,EAAA,GAAA,EAeAyG,GAbAf,EAAA3H,KAAA,kCAAA,EAAAnB,GAAA,aAAA,WACA,IAAAiG,EAAAtG,EAAAlC,IAAA,EAAA6D,IAAA,EAAAC,KAAA,EAGA0E,EACA2D,EAAA3D,EAHAxI,IAGA,EAGA4J,EANA5J,KAMA,uBAAA,OAAA,CAEA,CAAA,EAGA0E,EAAA,SAAAsD,EAAApE,GACAyG,EAAArC,EAAA,SAAA3D,EAAAsB,GACAiE,EAAAhG,EAAAS,EAAAsB,CAAA,CACA,CAAA,CACA,EAAA,GAAA,GAEA0F,EAAA3H,KAAA,mBAAA,EAAAnB,GAAA,aAAA,WACA,IAAAyF,EAAA9F,EAAAlC,IAAA,EAAA6D,IAAA,EAAAC,KAAA,EAGAkE,EACAoE,EAAApE,EAHAhI,IAGA,EAGA4J,EANA5J,KAMA,4BAAA,OAAA,CAEA,CAAA,EAGAqL,EAAA3H,KAAA,mBAAA,EAAAnB,GAAA,aAAA,WACA,IAAAkG,EAAAvG,EAAAlC,IAAA,EAAA6D,IAAA,EAAAC,KAAA,EACAuI,EAAA/B,EAAA7B,CAAA,EAEAA,EACAmB,EAAA5J,KAAAqM,EAAAhI,QAAAgI,EAAA5B,MAAA,UAAA,OAAA,EAGAb,EAAA5J,KAAA,2BAAA,OAAA,CAEA,CAAA,EAGA,IAAAuM,EAAAlB,EAAA3H,KAAA,0DAAA,EAKA6I,EAAAhK,GAAA,QAAA,YAEA,EAEAgK,EAAAhK,GAAA,cAAA,WAEA,IAAAmG,EAAAxG,EAAAlC,IAAA,EAAA6D,IAAA,EAEA6H,EAAAhB,EAAAhC,CAAA,EAEA8D,EAAAtK,EAAAlC,IAAA,EAAA4C,QAAA,aAAA,EAAAc,KAAA,8BAAA,EACA+I,EAAAD,EAAA9I,KAAA,gBAAA,EAEAgJ,EAAAF,EAAA9I,KAAA,eAAA,EA+BA4F,GA5BAkD,EAAA1J,YAAA,+CAAA,EACA,SAAA4I,EAAAf,UACA6B,EAAAlJ,SAAA,YAAAoI,EAAAf,QAAA,EACA8B,EAAA9E,KAAA+D,EAAAf,SAAAgC,OAAA,CAAA,EAAAC,YAAA,EAAAlB,EAAAf,SAAAK,MAAA,CAAA,CAAA,EAGA0B,EAAA5J,YAAA,uCAAA,EAAAQ,SAAA,aAAA,EACA,SAAAoI,EAAAf,SACA+B,EAAAG,GAAA,CAAA,EAAA/J,YAAA,aAAA,EAAAQ,SAAA,YAAA,EACA,WAAAoI,EAAAf,UACA+B,EAAAG,GAAA,CAAA,EAAA/J,YAAA,aAAA,EAAAQ,SAAA,eAAA,EACAoJ,EAAAG,GAAA,CAAA,EAAA/J,YAAA,aAAA,EAAAQ,SAAA,eAAA,GACA,WAAAoI,EAAAf,UACA+B,EAAA5J,YAAA,aAAA,EAAAQ,SAAA,cAAA,IAGAmJ,EAAA9E,KAAA,EAAA,EACA+E,EAAA5J,YAAA,uCAAA,EAAAQ,SAAA,aAAA,GAIA,EAAAoF,EAAA7F,OACA+G,EAAA5J,KAAA0L,EAAArH,QAAAqH,EAAAjB,MAAA,UAAA,OAAA,EAEAT,EAAAhK,IAAA,EAIAqL,EAAA3H,KAAA,kFAAA,EAAAG,IAAA,GACAyF,IACAwD,EAAA7B,EAAAvC,EAAAY,CAAA,EACAyD,EAAA1B,EAAA3H,KAAA,kFAAA,EAAA,KAEAkG,EAAAmD,EAAAD,EAAAzI,QAAAyI,EAAArC,MAAA,UAAA,OAAA,CAGA,CAAA,EAGAY,EAAA3H,KAAA,0DAAA,EAAAnB,GAAA,OAAA,WAEA,IADAL,EAAAlC,IAAA,EAAA6D,IAAA,EACAhB,QACA+G,EAAA5J,KAAA,uBAAA,OAAA,CAEA,CAAA,EAGAqL,EAAA3H,KAAA,kFAAA,EAAAnB,GAAA,cAAA,WACA,IAAAmG,EAAA2C,EAAA3H,KAAA,0DAAA,EAAAG,IAAA,EACAyF,EAAApH,EAAAlC,IAAA,EAAA6D,IAAA,EAEA,EAAAyF,EAAAzG,OAEA+G,EAAA5J,MADA2L,EAAAV,EAAAvC,EAAAY,CAAA,GACAjF,QAAAsH,EAAAlB,MAAA,UAAA,OAAA,EAEAT,EAAAhK,IAAA,CAEA,CAAA,EAGAqL,EAAA3H,KAAA,kFAAA,EAAAnB,GAAA,OAAA,WAEA,IADAL,EAAAlC,IAAA,EAAA6D,IAAA,EACAhB,QACA+G,EAAA5J,KAAA,+BAAA,OAAA,CAEA,CAAA,EAGAqL,EAAA3H,KAAA,kHAAA,EAAAnB,GAAA,aAAA,WACA,IAAAtC,EAAAiC,EAAAlC,IAAA,EAAA6D,IAAA,EAAAC,KAAA,EACAkJ,EAAA9K,EAAAlC,IAAA,EAAAiE,KAAA,IAAA,EACAT,IAAA2H,EAAA,YAGA,cAAA6B,GAAA,sBAAAA,GAAA,iBAAAA,EACA7B,EAAA,WACA,mBAAA6B,IACA7B,EAAA,iBAGA,IAAAkB,EAAAnB,EAAAjL,EAAAkL,CAAA,EAEA,GAAAlL,EACA2J,EAAA5J,KAAAqM,EAAAhI,QAAAgI,EAAA5B,MAAA,UAAA,OAAA,MACA,CAEAjH,IAAAa,EAAA,yBACA,cAAA2I,GAAA,sBAAAA,GAAA,iBAAAA,EACA3I,EAAA,wBACA,mBAAA2I,IACA3I,EAAA,8BAEAuF,EAAA5J,KAAAqE,EAAA,OAAA,CACA,CACA,CAAA,EAGAgH,EAAA3H,KAAA,gBAAA,EAAAnB,GAAA,aAAA,WACA,IAAAtC,EAAAiC,EAAAlC,IAAA,EAAA6D,IAAA,EAAAC,KAAA,EACAuI,EAAAnB,EAAAjL,EAAA,cAAA,EAEAA,EACA2J,EAAA5J,KAAAqM,EAAAhI,QAAAgI,EAAA5B,MAAA,UAAA,OAAA,EAEAT,EAAAhK,IAAA,CAEA,CAAA,CArLA,CA6ZA,CAjNAkC,EAAA7C,QAAA,EAAAkD,GAAA,QAAA,mBAAA,WACA,IAAAkF,EAAAvF,EAAAlC,IAAA,EACAiN,EAAAxF,EAAA3G,KAAA,QAAA,EACAoM,EAAAhL,EAAA,IAAA+K,CAAA,EACAE,EAAA1F,EAAA/D,KAAA,WAAA,EACA0J,EAAA3F,EAAA/D,KAAA,aAAA,EAEA,aAAAwJ,EAAAjJ,KAAA,MAAA,GACAiJ,EAAAjJ,KAAA,OAAA,MAAA,EACAkJ,EAAA7J,SAAA,QAAA,EACA8J,EAAAtK,YAAA,QAAA,IAEAoK,EAAAjJ,KAAA,OAAA,UAAA,EACAkJ,EAAArK,YAAA,QAAA,EACAsK,EAAA9J,SAAA,QAAA,EAEA,CAAA,EAqIApB,EAAA7C,QAAA,EAAAkD,GAAA,QAAA,2BAAA,SAAAC,GACAA,EAAAC,eAAA,EAEA,IAAA4K,EAAAnL,EAAAlC,IAAA,EACAsN,EAAApL,EAAA,8BAAA,EACA4F,EAAAuF,EAAAvM,KAAA,YAAA,EAEAgH,EAMA,aAAA,OAAAqC,iBAEAmD,EAAApF,KAAA,uHAAA,EAAAqF,KAAA,GAKAF,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,YAAA,EACA2F,EAAAE,KAAA,EAGAtL,EAAA+E,KAAA,CACAC,IAAAiD,iBAAA3J,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,8BACAY,MAAAF,EACAR,MAAA6C,iBAAA7C,KACA,EACAvG,QAAA,SAAAH,GACAA,EAAAG,SACAuM,EAAApF,KAAA,wFAAAtH,EAAAE,KAAAuD,QAAA,QAAA,EAAAkJ,KAAA,EACAF,EAAA1F,KAAA,aAAA,EAGAvC,WAAA,WACAiI,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,2BAAA,CACA,EAAA,GAAA,IAEA2F,EAAApF,KAAA,yFAAAtH,EAAAE,KAAAuD,SAAA,qCAAA,QAAA,EAAAkJ,KAAA,EACAF,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,2BAAA,EAEA,EACAnG,MAAA,SAAAiM,EAAAC,EAAAlM,GACAD,QAAAC,MAAA,uBAAAA,CAAA,EACAD,QAAAC,MAAA,yBAAAiM,EAAAE,YAAA,EACAL,EAAApF,KAAA,gIAAA,EAAAqF,KAAA,EACAF,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,2BAAA,CACA,CACA,CAAA,GA3CA2F,EAAApF,KAAA,0HAAA,EAAAqF,KAAA,CA4CA,CAAA,EAoSA,IAAAK,EAAA1L,EAAA,8BAAA,EACA2L,EAAA3L,EAAA,iBAAA,EAGA,GAFAA,EAAA,iBAAA,EAEA0L,EAAA/K,QAAAgL,EAAAhL,OAAA,CAKAiL,EAAA,IAAAC,gBAAA1K,OAAAoD,SAAAuH,MAAA,EACA,IAAAhG,EAAA8F,EAAAG,IAAA,OAAA,EACAC,EAAAJ,EAAAG,IAAA,OAAA,EA4MA,SAAAE,EAAA9J,GACA+J,EAAA/J,EAAA,SAAA,CACA,CAEA,SAAAgK,EAAAhK,GACA+J,EAAA/J,EAAA,OAAA,CACA,CAEA,SAAA+J,EAAA/J,EAAAsB,GACA,IAAA2I,EAAApM,EAAA,WAAA,EAGAoM,EAAApG;oDAFA,YAAAvC,EAAA,+CAAA;yBAIAtB;;aAEA,EAGA,YAAAsB,GACAP,WAAA,KACAkJ,EAAApG,KAAA,EAAA,CACA,EAAA,GAAA,CAEA,CAEA,SAAAqG,EAAAvG,GAEA,MADA,6BACA7D,KAAA6D,CAAA,CACA,CArOA,iBADA8F,EAAAG,IAAA,OAAA,EAEAI,EAAA,gFAAA,EAKArG,GAWA9F,EAAA+E,KAAA,CACAC,IAAAiD,iBAAA3J,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,oCACAY,MAAAA,EACAkG,MAAAA,GAAA,EACA,EACAnN,QAAA,SAAAH,GACA,GAAAA,EAAAG,QACAyN,CAAAA,IAWAC,EAXA7N,EAAAE,KAYA,IAAA+M,EAAA3L,EAAA,iBAAA,EACAwM,EAAAxM,EAAA,iBAAA,EAEAyM,EAAA,GACAC,EAAA,GAEA,OAAAH,EAAAf,QACA,IAAA,WACAkB,EAAA,+CACAD;;;;;;;oDAOAF,EAAApK;sBAEAnC,EAAA,gBAAA,EAAAY,YAAA,QAAA,EACA,MAEA,IAAA,mBACA8L,EAAA,kDACAD;;;;;;;qDAOAF,EAAApK;sBAEA,MAEA,IAAA,WACAuK,EAAA,4CACAD;;;;;;;mDAOAF,EAAApK;sBAEAnC,EAAA,gBAAA,EAAAY,YAAA,QAAA,EACA,MAEA,IAAA,aACA8L,EAAA,yCACAD;;;;;;;kDAOAF,EAAApK;sBAIAoK,EAAAI,YACA3M,EAAA,iBAAA,EAAAY,YAAA,QAAA,EAEAZ,EAAA,uBAAA,EAAAY,YAAA,QAAA,EACAZ,EAAA,gBAAA,EAAA2B,IAAAmE,CAAA,EAGAyG,EAAAK,wBACAH,kEAAAF,EAAAK,8BAGA,CAEAjB,EAAA3F,sDAAA0G,MAAAD,SAAA,EACAD,EAAA5L,YAAA,QAAA,CAvFA,MAEAuL,EAAAzN,EAAAE,KAAAuD,SAAA,qCAAA,CAEA,EACA7C,MAAA,WACA6M,EAAA,qCAAA,CACA,CACA,CAAA,EAmFAnM,EAAA7C,QAAA,EAAAkD,GAAA,QAAA,2BAAA,WACA,IAAA8K,EAAAnL,EAAAlC,IAAA,EACAqN,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,YAAA,EAEAzF,EAAA+E,KAAA,CACAC,IAAAiD,iBAAA3J,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,8BACAY,MAAAA,EACAV,MAAA6C,iBAAA7C,KACA,EACAvG,QAAA,SAAAH,GACAA,EAAAG,SACAoN,EAAAvN,EAAAE,KAAAuD,OAAA,EACAgJ,EAAA1F,KAAA,aAAA,EACAvC,WAAA,KACAiI,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,2BAAA,CACA,EAAA,GAAA,IAEA0G,EAAAzN,EAAAE,KAAAuD,SAAA,mCAAA,EACAgJ,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,2BAAA,EAEA,EACAnG,MAAA,WACA6M,EAAA,qCAAA,EACAhB,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,2BAAA,CACA,CACA,CAAA,CACA,CAAA,EAGAzF,EAAA7C,QAAA,EAAAkD,GAAA,SAAA,qBAAA,SAAAC,GACAA,EAAAC,eAAA,EAEA,IAAAsM,EAAA7M,EAAA,YAAA,EAAA2B,IAAA,EACAwJ,EAAAnL,EAAA,mBAAA,EAEA6M,GAAAR,EAAAQ,CAAA,EAKAA,IAAA/G,EACAqG,EAAA,gDAAA,GAIAhB,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,aAAA,EAEAzF,EAAA+E,KAAA,CACAC,IAAAiD,iBAAA3J,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,+BACA4H,UAAAhH,EACAiH,UAAAF,EACAb,MAAAA,GAAA,GACA5G,MAAA6C,iBAAA7C,KACA,EACAvG,QAAA,SAAAH,GACA,IAGAsO,EAHAtO,EAAAG,SACAoN,EAAAvN,EAAAE,KAAAuD,OAAA,GAEA6K,EAAA,IAAAC,IAAA9L,OAAAoD,QAAA,GACA2I,aAAAC,IAAA,QAAAN,CAAA,EACA1L,OAAAiM,QAAAC,aAAA,GAAA,GAAAL,CAAA,EACA9J,WAAA,KACA/B,OAAAoD,SAAAC,OAAA,CACA,EAAA,GAAA,IAEA2H,EAAAzN,EAAAE,KAAAuD,SAAA,gCAAA,EACAgJ,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,sBAAA,EAEA,EACAnG,MAAA,WACA6M,EAAA,qCAAA,EACAhB,EAAA9F,KAAA,WAAA,CAAA,CAAA,EAAAI,KAAA,sBAAA,CACA,CACA,CAAA,GAxCA0G,EAAA,oCAAA,CAyCA,CAAA,IA/LAA,EAAA,sHAAA,GAgOA,KACA,IAAAR,EAAA3L,EAAA,iBAAA,EACAwM,EAAAxM,EAAA,iBAAA,EAEA2L,EAAA3F;;;;;;;;;;;;;;aAcA,EAGAhG,EAAA7C,QAAA,EAAAkD,GAAA,SAAA,oBAAA,SAAAC,GACAA,EAAAC,eAAA,EACA,IAQAyM,EARAM,EAAAtN,EAAA,oBAAA,EAAA2B,IAAA,EAAAC,KAAA,EAEA0L,GAAAjB,EAAAiB,CAAA,IAMAN,EAAA,IAAAC,IAAA9L,OAAAoD,QAAA,GACA2I,aAAAC,IAAA,QAAAG,CAAA,EACAnM,OAAAoD,SAAAgJ,KAAAP,EAAAQ,SAAA,GAPArB,EAAA,oCAAA,CAQA,CAAA,EAEAK,EAAA5L,YAAA,QAAA,CACA,GAnQA,EAlBA,CA7RA6M,CA5IA,IAAAC,EAAA1N,EAAA,oBAAA,EACA2N,EAAA3N,EAAA,iCAAA,EAEA,GAAA,IAAA0N,EAAA/M,QAAA,IAAAgN,EAAAhN,OAKA,GAAA,aAAA,OAAAsH,iBACA5I,QAAAG,KAAA,gEAAA,MADA,CAMA8B,IAAAsM,EAAA,GA6BA,SAAAC,EAAApK,GACA,IAAAqK,EAAA3Q,SAAAQ,eAAA,sBAAA,EACAoQ,EAAA,cAAAtK,EAAA,YAAA,uBAEAmK,EAAAG,GAEAD,EAAA9P,UAAA4P,EAAAG,GAGAD,EAAA9P,UAAA,sDACA0P,EAAA9M,YAAA,QAAA,CAEA,CAtCAZ,EAAA+E,KAAA,CACAC,IAAAiD,iBAAA3J,SACAmF,KAAA,OACA7E,KAAA,CACAsG,OAAA,yBACAE,MAAA6C,iBAAA7C,KACA,EACAvG,QAAA,SAAAH,GACA,IAIAsP,EAJAtP,EAAAG,SAAAH,EAAAE,MACAgP,EAAAlP,EAAAE,MAGAoP,EAAAL,EAAAhE,OAAA,UAAA,GACAhJ,QACAkN,EAAAG,EAAArM,IAAA,CAAA,GAGAtC,QAAAC,MAAA,mDAAAZ,CAAA,CAEA,EACAY,MAAA,SAAAiM,EAAAC,EAAAlM,GACAD,QAAAC,MAAA,kDAAAA,CAAA,CACA,CACA,CAAA,EAkBAqO,EAAAtN,GAAA,SAAA,WACAvC,KAAAmQ,UACAJ,EAAA/P,KAAAC,KAAA,EAGAiC,EAAA,2BAAA,EAAAY,YAAA,8BAAA,EAAAQ,SAAA,iBAAA,EAEApB,EAAAlC,IAAA,EAAA4C,QAAA,2BAAA,EACAE,YAAA,iBAAA,EAAAQ,SAAA,8BAAA,EAEA,CAAA,CAzDA,CAiIA,CAGApB,EAAA,MAAA,EAAA2J,OAAA,WACA,IAAAuE,EAAA,EAAAlO,EAAAlC,IAAA,EAAA0D,KAAA,wBAAA,EAAAb,OACAwN,EAAA,EAAAnO,EAAAlC,IAAA,EAAA0D,KAAA,mDAAA,EAAAb,OACAyN,EAAA,EAAApO,EAAAlC,IAAA,EAAA0D,KAAA,wBAAA,EAAAb,OACA,OAAAuN,GAAAC,GAAAC,CACA,CAAA,EAGApO,EAAA,4BAAA,EAAAK,GAAA,SAAA,SAAAC,GACAA,EAAAC,eAAA,EAEA,IAAA4I,EAAAnJ,EAAAlC,IAAA,EACAuQ,EAAAlF,EAAA3H,KAAA,uBAAA,EACA8M,EAAAD,EAAA7M,KAAA,WAAA,EACA+M,EAAAF,EAAA7M,KAAA,cAAA,EACA4K,EAAApM,EAAA,wBAAA,EAGAwO,EAAAtF,EAAAC,CAAA,EAEA,GAAAqF,EAAAjN,QAAA,CAwBA8M,EAAAhJ,KAAA,WAAA,CAAA,CAAA,EACAiJ,EAAAlN,SAAA,QAAA,EACAmN,EAAA3N,YAAA,QAAA,EACAwL,EAAAhL,SAAA,QAAA,EAGA,IAAAxD,EAAA,IAAAM,SAAAiL,EAAA,EAAA,EACAvL,EAAAO,OAAA,SAAA,uBAAA,EAGA6B,EAAA+E,KAAA,CACAC,IAAAiD,iBAAA3J,SACAmF,KAAA,OACA7E,KAAAhB,EACA6Q,YAAA,CAAA,EACAC,YAAA,CAAA,EACA7P,QAAA,SAAAH,GACAA,EAAAG,SAEAuN,EAAApG,KAAA,qJAEAtH,EAAAE,KAAAuD,QACA,YAAA,EACAiK,EAAAxL,YAAA,QAAA,EAGAuI,EAAA,GAAA/E,MAAA,EAGA+E,EAAA3H,KAAA,iBAAA,EAAAJ,SAAA,QAAA,EACA+H,EAAA3H,KAAA,OAAA,EAAAZ,YAAA,iCAAA,EAAAQ,SAAA,iBAAA,EAGA+H,EAAA3H,KAAA,8BAAA,EAAAZ,YAAA,+CAAA,EACAuI,EAAA3H,KAAA,gBAAA,EAAAiE,KAAA,EAAA,EACA0D,EAAA3H,KAAA,eAAA,EAAAZ,YAAA,uCAAA,EAAAQ,SAAA,aAAA,EAGA+H,EAAA3H,KAAA,iDAAA,EAAA6D,KAAA,UAAA,CAAA,CAAA,EACArF,EAAA,oBAAA,EAAAY,YAAA,QAAA,EACAZ,EAAA,mBAAA,EAAAoB,SAAA,QAAA,EAGAgL,EAAA,GAAAuC,eAAA,CAAAC,SAAA,SAAAC,MAAA,QAAA,CAAA,IAIAzC,EAAApG,KAAA,2IAEAtH,EAAAE,KAAAuD,SAAA,wCACA,YAAA,EACAiK,EAAAxL,YAAA,QAAA,EAEA,EACAtB,MAAA,SAAAiM,EAAAC,EAAAlM,GACAD,QAAAC,MAAA,+BAAAA,CAAA,EAEAgC,IAAAwN,EAAA,qBACA,MAAAvD,EAAAC,OACAsD,GAAA,8DACA,IAAAvD,EAAAC,OACAsD,GAAA,wFAEAA,GAAA,kDAGA1C,EAAApG,KAAA,0IAEA8I,EACA,YAAA,EACA1C,EAAAxL,YAAA,QAAA,CACA,EACAmO,SAAA,WAEAV,EAAAhJ,KAAA,WAAA,CAAA,CAAA,EACAiJ,EAAA1N,YAAA,QAAA,EACA2N,EAAAnN,SAAA,QAAA,CACA,CACA,CAAA,CAjFA,KArBA,CAEAE,IAAA0N,EAAA,gFAEAA,EADAA,EAAA,uEACA,+CAEAR,EAAApF,OAAA9L,QAAA,SAAAgC,GACA0P,GAAA,OAAA1P,EAAA,OACA,CAAA,EAEA0P,GAAA,cACA5C,EAAApG,KAAAgJ,CAAA,EACA5C,EAAAxL,YAAA,QAAA,EAGAqO,EAAA9F,EAAA3H,KAAA,iBAAA,EAAA0N,MAAA,EACA,KAAAD,EAAAtO,SACAsO,EAAA,GAAAN,eAAA,CAAAC,SAAA,SAAAC,MAAA,QAAA,CAAA,EACAI,EAAAE,MAAA,GAGA,CAkFA,CAAA,EAGAnP,EAAA,2CAAA,EAAAK,GAAA,SAAA,SAAAC,GACAA,EAAAC,eAAA,EAEA,IAAA4I,EAAAnJ,EAAAlC,IAAA,EACAuQ,EAAAlF,EAAA3H,KAAA,uBAAA,EACA8M,EAAAD,EAAA7M,KAAA,cAAA,EACA+M,EAAAF,EAAA7M,KAAA,eAAA,EACA4K,EAAApM,EAAA,gBAAA,EAGAwO,EAAAtF,EAAAC,CAAA,EAEA,GAAAqF,EAAAjN,QAAA,CAwBA8M,EAAAhJ,KAAA,WAAA,CAAA,CAAA,EACAiJ,EAAAlN,SAAA,QAAA,EACAmN,EAAA3N,YAAA,QAAA,EACAwL,EAAAhL,SAAA,QAAA,EAGA,IAAAxD,EAAA,IAAAM,SAAAiL,EAAA,EAAA,EACAvL,EAAAO,OAAA,SAAA,iCAAA,EAGA6B,EAAA+E,KAAA,CACAC,IAAAiD,iBAAA3J,SACAmF,KAAA,OACA7E,KAAAhB,EACA6Q,YAAA,CAAA,EACAC,YAAA,CAAA,EACA7P,QAAA,SAAAH,GACAA,EAAAG,SAEAuN,EAAApG,KAAA,qJAEAtH,EAAAE,KAAAuD,QACA,YAAA,EACAiK,EAAAxL,YAAA,QAAA,EAGAuI,EAAA,GAAA/E,MAAA,EAGA+E,EAAA3H,KAAA,iBAAA,EAAAJ,SAAA,QAAA,EACA+H,EAAA3H,KAAA,OAAA,EAAAZ,YAAA,iCAAA,EAAAQ,SAAA,iBAAA,EAGA+H,EAAA3H,KAAA,8BAAA,EAAAZ,YAAA,+CAAA,EACAuI,EAAA3H,KAAA,gBAAA,EAAAiE,KAAA,EAAA,EACA0D,EAAA3H,KAAA,eAAA,EAAAZ,YAAA,uCAAA,EAAAQ,SAAA,aAAA,EAGAgL,EAAA,GAAAuC,eAAA,CAAAC,SAAA,SAAAC,MAAA,QAAA,CAAA,IAIAzC,EAAApG,KAAA,2IAEAtH,EAAAE,KAAAuD,SAAA,wCACA,YAAA,EACAiK,EAAAxL,YAAA,QAAA,EAEA,EACAtB,MAAA,WAEA8M,EAAApG,KAAA,qOAGA,EACAoG,EAAAxL,YAAA,QAAA,CACA,EACAmO,SAAA,WAEAV,EAAAhJ,KAAA,WAAA,CAAA,CAAA,EACAiJ,EAAA1N,YAAA,QAAA,EACA2N,EAAAnN,SAAA,QAAA,CACA,CACA,CAAA,CAlEA,KArBA,CAEAE,IAAA0N,EAAA,gFAEAA,EADAA,EAAA,uEACA,+CAEAR,EAAApF,OAAA9L,QAAA,SAAAgC,GACA0P,GAAA,OAAA1P,EAAA,OACA,CAAA,EAEA0P,GAAA,cACA5C,EAAApG,KAAAgJ,CAAA,EACA5C,EAAAxL,YAAA,QAAA,EAGAqO,EAAA9F,EAAA3H,KAAA,iBAAA,EAAA0N,MAAA,EACA,KAAAD,EAAAtO,SACAsO,EAAA,GAAAN,eAAA,CAAAC,SAAA,SAAAC,MAAA,QAAA,CAAA,EACAI,EAAAE,MAAA,GAGA,CAmEA,CAAA,EAGA7N,IAAA8N,EAAApP,EAAA,iEAAA,GAEAoP,EADAA,EAAAzO,OAWAyO,EAVApP,EAAA,MAAA,EAAA2J,OAAA,WACA,IAAAuE,EAAA,EAAAlO,EAAAlC,IAAA,EAAA0D,KAAA,wBAAA,EAAAb,OAEAyN,GADApO,EAAAlC,IAAA,EAAA0D,KAAA,mDAAA,EAAAb,OACA,EAAAX,EAAAlC,IAAA,EAAA0D,KAAA,wBAAA,EAAAb,QACA0O,EAAArP,EAAAlC,IAAA,EAAAiE,KAAA,IAAA,EAEA,OAAAmM,GAAAE,GAAA,6CAAAiB,CACA,CAAA,GAGAhP,GAAA,SAAA,SAAAC,GACA,IAAAkO,EAAAtF,EAAAlJ,EAAAlC,IAAA,CAAA,EAEA,GAAA,CAAA0Q,EAAAjN,QAAA,CACAjB,EAAAC,eAAA,EAGA6L,EAAApM,EAAAlC,IAAA,EAAA0D,KAAA,mBAAA,EACA,GAAA4K,EAAAzL,OAAA,CACAW,IAAA0N,EAAA,gFAEAA,EADAA,EAAA,uEACA,+CAEAR,EAAApF,OAAA9L,QAAA,SAAAgC,GACA0P,GAAA,OAAA1P,EAAA,OACA,CAAA,EAEA0P,GAAA,cACA5C,EAAApG,KAAAgJ,CAAA,EACA5C,EAAAxL,YAAA,QAAA,CACA,CAGAqO,EAAAjP,EAAAlC,IAAA,EAAA0D,KAAA,iBAAA,EAAA0N,MAAA,EACAD,EAAAtO,SACAsO,EAAA,GAAAN,eAAA,CAAAC,SAAA,SAAAC,MAAA,QAAA,CAAA,EACAI,EAAAE,MAAA,EAEA,CACA,CAAA,CACA,CAAA,CAmSA,GAAA7L,MAAA", "file": "main.min.js", "sourcesContent": ["/**\r\n * Location Selector JavaScript\r\n * Handles dynamic loading of cities/towns based on province selection\r\n */\r\n\r\ndocument.addEventListener('DOMContentLoaded', function() {\r\n    // Find all province selectors\r\n    const provinceSelectors = document.querySelectorAll('[data-location-province]');\r\n    \r\n    provinceSelectors.forEach(function(provinceSelect) {\r\n        const citySelectId = provinceSelect.getAttribute('data-location-province');\r\n        const citySelect = document.getElementById(citySelectId);\r\n        \r\n        if (!citySelect) {\r\n            console.warn('City select element not found:', citySelectId);\r\n            return;\r\n        }\r\n        \r\n        provinceSelect.addEventListener('change', function() {\r\n            const provinceId = this.value;\r\n            \r\n            // Clear city options\r\n            citySelect.innerHTML = '<option value=\"\">Select City/Town...</option>';\r\n            citySelect.disabled = true;\r\n            \r\n            if (!provinceId) {\r\n                return;\r\n            }\r\n            \r\n            // Show loading state\r\n            citySelect.innerHTML = '<option value=\"\">Loading cities...</option>';\r\n            \r\n            // Make AJAX request\r\n            const formData = new FormData();\r\n            formData.append('action', 'get_cities_by_province');\r\n            formData.append('province_id', provinceId);\r\n            \r\n            fetch(autohub_ajax.ajax_url, {\r\n                method: 'POST',\r\n                body: formData\r\n            })\r\n            .then(response => response.json())\r\n            .then(data => {\r\n                if (data.success) {\r\n                    // Clear loading state\r\n                    citySelect.innerHTML = '<option value=\"\">Select City/Town...</option>';\r\n                    \r\n                    // Add city options\r\n                    data.data.forEach(function(city) {\r\n                        const option = document.createElement('option');\r\n                        option.value = city.id;\r\n                        option.textContent = city.title;\r\n                        citySelect.appendChild(option);\r\n                    });\r\n                    \r\n                    citySelect.disabled = false;\r\n                } else {\r\n                    citySelect.innerHTML = '<option value=\"\">Error loading cities</option>';\r\n                    console.error('Error loading cities:', data);\r\n                }\r\n            })\r\n            .catch(error => {\r\n                citySelect.innerHTML = '<option value=\"\">Error loading cities</option>';\r\n                console.error('AJAX error:', error);\r\n            });\r\n        });\r\n    });\r\n});\r\n\r\n/**\r\n * Alpine.js component for location selection (if using Alpine.js)\r\n */\r\nif (typeof Alpine !== 'undefined') {\r\n    Alpine.data('locationSelector', () => ({\r\n        selectedProvince: '',\r\n        selectedCity: '',\r\n        cities: [],\r\n        loadingCities: false,\r\n        \r\n        async loadCities() {\r\n            if (!this.selectedProvince) {\r\n                this.cities = [];\r\n                this.selectedCity = '';\r\n                return;\r\n            }\r\n            \r\n            this.loadingCities = true;\r\n            this.selectedCity = '';\r\n            \r\n            try {\r\n                const formData = new FormData();\r\n                formData.append('action', 'get_cities_by_province');\r\n                formData.append('province_id', this.selectedProvince);\r\n                \r\n                const response = await fetch(autohub_ajax.ajax_url, {\r\n                    method: 'POST',\r\n                    body: formData\r\n                });\r\n                \r\n                const data = await response.json();\r\n                \r\n                if (data.success) {\r\n                    this.cities = data.data;\r\n                } else {\r\n                    console.error('Error loading cities:', data);\r\n                    this.cities = [];\r\n                }\r\n            } catch (error) {\r\n                console.error('AJAX error:', error);\r\n                this.cities = [];\r\n            } finally {\r\n                this.loadingCities = false;\r\n            }\r\n        }\r\n    }));\r\n}", "/**\r\n * AutoHub Zambia Theme JavaScript\r\n * Main JavaScript file for theme functionality\r\n */\r\n\r\n(function($) {\r\n    'use strict';\r\n\r\n    // DOM Ready\r\n    $(document).ready(function() {\r\n        initializeTheme();\r\n    });\r\n\r\n    // Initialize theme functionality\r\n    function initializeTheme() {\r\n        initMobileMenu();\r\n        initSmoothScroll();\r\n        initBackToTop();\r\n        initFormValidation();\r\n    }\r\n\r\n    // Mobile menu toggle\r\n    function initMobileMenu() {\r\n        const mobileMenuButton = $('.mobile-menu-button');\r\n        const mobileMenu = $('.mobile-menu');\r\n\r\n        mobileMenuButton.on('click', function(e) {\r\n            e.preventDefault();\r\n            $(this).toggleClass('active');\r\n            mobileMenu.toggleClass('active');\r\n            $('body').toggleClass('menu-open');\r\n        });\r\n\r\n        // Close mobile menu when clicking outside\r\n        $(document).on('click', function(e) {\r\n            if (!$(e.target).closest('.mobile-menu, .mobile-menu-button').length) {\r\n                mobileMenuButton.removeClass('active');\r\n                mobileMenu.removeClass('active');\r\n                $('body').removeClass('menu-open');\r\n            }\r\n        });\r\n    }\r\n\r\n    // Smooth scroll for anchor links\r\n    function initSmoothScroll() {\r\n        $('a[href*=\"#\"]:not([href=\"#\"])').on('click', function(e) {\r\n            const target = $(this.getAttribute('href'));\r\n            if (target.length) {\r\n                e.preventDefault();\r\n                $('html, body').animate({\r\n                    scrollTop: target.offset().top - 80\r\n                }, 800);\r\n            }\r\n        });\r\n    }\r\n\r\n    // Back to top button\r\n    function initBackToTop() {\r\n        const backToTop = $('.back-to-top');\r\n        \r\n        if (backToTop.length) {\r\n            $(window).on('scroll', function() {\r\n                if ($(this).scrollTop() > 300) {\r\n                    backToTop.addClass('visible');\r\n                } else {\r\n                    backToTop.removeClass('visible');\r\n                }\r\n            });\r\n\r\n            backToTop.on('click', function(e) {\r\n                e.preventDefault();\r\n                $('html, body').animate({\r\n                    scrollTop: 0\r\n                }, 800);\r\n            });\r\n        }\r\n    }\r\n\r\n    // Basic form validation\r\n    function initFormValidation() {\r\n        $('form').on('submit', function(e) {\r\n            const form = $(this);\r\n            let isValid = true;\r\n\r\n            // Check required fields\r\n            form.find('[required]').each(function() {\r\n                const field = $(this);\r\n                const value = field.val().trim();\r\n\r\n                if (!value) {\r\n                    isValid = false;\r\n                    field.addClass('error');\r\n                    showFieldError(field, 'This field is required.');\r\n                } else {\r\n                    field.removeClass('error');\r\n                    hideFieldError(field);\r\n                }\r\n\r\n                // Email validation\r\n                if (field.attr('type') === 'email' && value) {\r\n                    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n                    if (!emailRegex.test(value)) {\r\n                        isValid = false;\r\n                        field.addClass('error');\r\n                        showFieldError(field, 'Please enter a valid email address.');\r\n                    }\r\n                }\r\n            });\r\n\r\n            if (!isValid) {\r\n                e.preventDefault();\r\n            }\r\n        });\r\n\r\n        // Remove error styling on input\r\n        $('input, textarea, select').on('input change', function() {\r\n            const field = $(this);\r\n            if (field.hasClass('error')) {\r\n                field.removeClass('error');\r\n                hideFieldError(field);\r\n            }\r\n        });\r\n    }\r\n\r\n    // Show field error message\r\n    function showFieldError(field, message) {\r\n        hideFieldError(field);\r\n        const errorDiv = $('<div class=\"field-error text-red-600 text-sm mt-1\">' + message + '</div>');\r\n        field.after(errorDiv);\r\n    }\r\n\r\n    // Hide field error message\r\n    function hideFieldError(field) {\r\n        field.next('.field-error').remove();\r\n    }\r\n\r\n    // Utility function for debouncing\r\n    function debounce(func, wait, immediate) {\r\n        let timeout;\r\n        return function() {\r\n            const context = this;\r\n            const args = arguments;\r\n            const later = function() {\r\n                timeout = null;\r\n                if (!immediate) func.apply(context, args);\r\n            };\r\n            const callNow = immediate && !timeout;\r\n            clearTimeout(timeout);\r\n            timeout = setTimeout(later, wait);\r\n            if (callNow) func.apply(context, args);\r\n        };\r\n    }\r\n\r\n    // Window resize handler with debounce\r\n    $(window).on('resize', debounce(function() {\r\n        // Handle responsive adjustments here\r\n    }, 250));\r\n\r\n    // Expose functions globally if needed\r\n    window.AutoHubTheme = {\r\n        initializeTheme: initializeTheme,\r\n        debounce: debounce\r\n    };\r\n\r\n})(jQuery);\r\n\r\n/**\r\n * Vehicle Management JavaScript\r\n * Handles vehicle form submissions and related functionality\r\n */\r\ndocument.addEventListener('DOMContentLoaded', function() {\r\n    // Handle Add Vehicle Form Submission\r\n    const addVehicleForm = document.getElementById('add-vehicle-form');\r\n    \r\n    if (addVehicleForm) {\r\n        addVehicleForm.addEventListener('submit', function(e) {\r\n            e.preventDefault();\r\n            \r\n            const submitButton = this.querySelector('button[type=\"submit\"]');\r\n            const originalText = submitButton.textContent;\r\n            \r\n            // Show loading state\r\n            submitButton.disabled = true;\r\n            submitButton.innerHTML = '<svg class=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white inline\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\"><circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle><path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path></svg>Adding Vehicle...';\r\n            \r\n            // Prepare form data\r\n            const formData = new FormData(this);\r\n            formData.append('action', 'add_vehicle');\r\n            formData.append('nonce', document.querySelector('#add_vehicle_nonce').value);\r\n            \r\n            // Send AJAX request\r\n            fetch(autohub_ajax.ajax_url, {\r\n                method: 'POST',\r\n                body: formData\r\n            })\r\n            .then(response => response.json())\r\n            .then(data => {\r\n                if (data.success) {\r\n                    // Show success message\r\n                    showNotification('success', data.data.message);\r\n                    \r\n                    // Reset form\r\n                    addVehicleForm.reset();\r\n                    \r\n                    // Close modal (using Alpine.js)\r\n                    const modalElement = document.querySelector('[x-data]');\r\n                    if (modalElement && modalElement._x_dataStack) {\r\n                        modalElement._x_dataStack[0].showAddVehicleModal = false;\r\n                    }\r\n                    \r\n                    // Refresh the page to show the new vehicle\r\n                    setTimeout(() => {\r\n                        window.location.reload();\r\n                    }, 1500);\r\n                    \r\n                } else {\r\n                    showNotification('error', data.data || 'An error occurred while adding the vehicle.');\r\n                }\r\n            })\r\n            .catch(error => {\r\n                console.error('Error:', error);\r\n                showNotification('error', 'An error occurred while adding the vehicle.');\r\n            })\r\n            .finally(() => {\r\n                // Reset button state\r\n                submitButton.disabled = false;\r\n                submitButton.textContent = originalText;\r\n            });\r\n        });\r\n    }\r\n    \r\n    // Notification function\r\n    function showNotification(type, message) {\r\n        // Create notification element\r\n        const notification = document.createElement('div');\r\n        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full ${\r\n            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'\r\n        }`;\r\n        notification.innerHTML = `\r\n            <div class=\"flex items-center\">\r\n                <svg class=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    ${type === 'success' \r\n                        ? '<path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>'\r\n                        : '<path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>'\r\n                    }\r\n                </svg>\r\n                <span>${message}</span>\r\n            </div>\r\n        `;\r\n        \r\n        document.body.appendChild(notification);\r\n        \r\n        // Animate in\r\n        setTimeout(() => {\r\n            notification.classList.remove('translate-x-full');\r\n        }, 100);\r\n        \r\n        // Remove after 5 seconds\r\n        setTimeout(() => {\r\n            notification.classList.add('translate-x-full');\r\n            setTimeout(() => {\r\n                if (notification.parentNode) {\r\n                    notification.parentNode.removeChild(notification);\r\n                }\r\n            }, 300);\r\n        }, 5000);\r\n    }\r\n});", "/**\r\n * AutoHub User Registration Admin Scripts\r\n * Handles admin functionality for pending users management\r\n */\r\n\r\n(function($) {\r\n    'use strict';\r\n\r\n    $(document).ready(function() {\r\n\r\n\r\n        // Handle auto-approve toggle\r\n        $('#auto-approve-toggle').on('change', function() {\r\n            const isChecked = $(this).is(':checked');\r\n            \r\n            $.ajax({\r\n                url: autohub_admin_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: {\r\n                    action: 'autohub_toggle_auto_approve',\r\n                    auto_approve: isChecked ? 'yes' : 'no',\r\n                    nonce: autohub_admin_ajax.nonce\r\n                },\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        // Auto-approve setting updated successfully\r\n                    } else {\r\n                        console.error('AutoHub Admin: Failed to update auto-approve setting');\r\n                        // Revert checkbox state\r\n                        $('#auto-approve-toggle').prop('checked', !isChecked);\r\n                    }\r\n                },\r\n                error: function() {\r\n                    console.error('AutoHub Admin: Error updating auto-approve setting');\r\n                    // Revert checkbox state\r\n                    $('#auto-approve-toggle').prop('checked', !isChecked);\r\n                }\r\n            });\r\n        });\r\n\r\n        // Handle user approval\r\n        $('.approve-user').on('click', function() {\r\n            const userId = $(this).data('user-id');\r\n            const $button = $(this);\r\n            const $row = $button.closest('tr');\r\n            \r\n            $button.prop('disabled', true).text('Approving...');\r\n            \r\n            $.ajax({\r\n                url: autohub_admin_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: {\r\n                    action: 'autohub_approve_user',\r\n                    user_id: userId,\r\n                    nonce: autohub_admin_ajax.nonce\r\n                },\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        $row.fadeOut(500, function() {\r\n                            $(this).remove();\r\n                        });\r\n                        \r\n                        // Show success message\r\n                        showAdminMessage('User approved successfully!', 'success');\r\n                    } else {\r\n                        showAdminMessage(response.data.message || 'Failed to approve user', 'error');\r\n                        $button.prop('disabled', false).text('Approve');\r\n                    }\r\n                },\r\n                error: function() {\r\n                    showAdminMessage('Error approving user', 'error');\r\n                    $button.prop('disabled', false).text('Approve');\r\n                }\r\n            });\r\n        });\r\n\r\n        // Handle user rejection\r\n        $('.reject-user').on('click', function() {\r\n            const userId = $(this).data('user-id');\r\n            const $button = $(this);\r\n            const $row = $button.closest('tr');\r\n            \r\n            if (!confirm('Are you sure you want to reject this user? This action cannot be undone.')) {\r\n                return;\r\n            }\r\n            \r\n            $button.prop('disabled', true).text('Rejecting...');\r\n            \r\n            $.ajax({\r\n                url: autohub_admin_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: {\r\n                    action: 'autohub_reject_user',\r\n                    user_id: userId,\r\n                    nonce: autohub_admin_ajax.nonce\r\n                },\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        $row.fadeOut(500, function() {\r\n                            $(this).remove();\r\n                        });\r\n                        \r\n                        // Show success message\r\n                        showAdminMessage('User rejected successfully!', 'success');\r\n                    } else {\r\n                        showAdminMessage(response.data.message || 'Failed to reject user', 'error');\r\n                        $button.prop('disabled', false).text('Reject');\r\n                    }\r\n                },\r\n                error: function() {\r\n                    showAdminMessage('Error rejecting user', 'error');\r\n                    $button.prop('disabled', false).text('Reject');\r\n                }\r\n            });\r\n        });\r\n\r\n        // Handle resend verification\r\n        $('.resend-verification').on('click', function() {\r\n            const userId = $(this).data('user-id');\r\n            const userEmail = $(this).data('user-email');\r\n            const $button = $(this);\r\n            const $statusCell = $button.closest('tr').find('td:nth-child(7)'); // Status column\r\n            \r\n            $button.prop('disabled', true).text('Sending...');\r\n            \r\n            $.ajax({\r\n                url: autohub_admin_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: {\r\n                    action: 'autohub_resend_verification',\r\n                    email: userEmail,\r\n                    nonce: autohub_admin_ajax.nonce\r\n                },\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        showAdminMessage(response.data.message, 'success');\r\n                        $button.text('Email Sent!');\r\n                        \r\n                        // Update status cell to show new attempt info\r\n                        const currentTime = new Date();\r\n                        const timeString = 'just now';\r\n                        $statusCell.html('<span style=\"color: #dba617;\">✉ Email Not Verified</span><br><small>Last sent: ' + timeString + '</small>');\r\n                        \r\n                        // Re-enable button after 30 seconds\r\n                        setTimeout(function() {\r\n                            $button.prop('disabled', false).text('Resend Verification');\r\n                        }, 30000);\r\n                    } else {\r\n                        showAdminMessage(response.data.message || 'Failed to send verification email', 'error');\r\n                        $button.prop('disabled', false).text('Resend Verification');\r\n                    }\r\n                },\r\n                error: function() {\r\n                    showAdminMessage('Error sending verification email', 'error');\r\n                    $button.prop('disabled', false).text('Resend Verification');\r\n                }\r\n            });\r\n        });\r\n\r\n        // Bulk actions functionality\r\n        $('#bulk-action-selector-top, #bulk-action-selector-bottom').on('change', function() {\r\n            const action = $(this).val();\r\n            const $checkboxes = $('.user-checkbox:checked');\r\n            \r\n            if (action === 'resend-verification' && $checkboxes.length > 0) {\r\n                if (confirm('Send verification emails to ' + $checkboxes.length + ' selected users?')) {\r\n                    processBulkResendVerification($checkboxes);\r\n                }\r\n            }\r\n        });\r\n\r\n        // Process bulk resend verification\r\n        function processBulkResendVerification($checkboxes) {\r\n            let processed = 0;\r\n            const total = $checkboxes.length;\r\n            \r\n            $checkboxes.each(function() {\r\n                const userId = $(this).val();\r\n                const userEmail = $(this).data('user-email');\r\n                const $row = $(this).closest('tr');\r\n                \r\n                $.ajax({\r\n                    url: autohub_admin_ajax.ajax_url,\r\n                    type: 'POST',\r\n                    data: {\r\n                        action: 'autohub_resend_verification',\r\n                        email: userEmail,\r\n                        nonce: autohub_admin_ajax.nonce\r\n                    },\r\n                    success: function(response) {\r\n                        processed++;\r\n                        if (response.success) {\r\n                            $row.find('.resend-verification').text('Email Sent!');\r\n                        }\r\n                        \r\n                        if (processed === total) {\r\n                            showAdminMessage('Bulk verification emails processed!', 'success');\r\n                        }\r\n                    },\r\n                    error: function() {\r\n                        processed++;\r\n                        if (processed === total) {\r\n                            showAdminMessage('Bulk verification emails completed with some errors', 'warning');\r\n                        }\r\n                    }\r\n                });\r\n            });\r\n        }\r\n\r\n        // Show admin message\r\n        function showAdminMessage(message, type) {\r\n            const $notice = $('<div class=\"notice notice-' + type + ' is-dismissible\"><p>' + message + '</p></div>');\r\n            $('.wrap h1').after($notice);\r\n            \r\n            // Auto-dismiss after 5 seconds\r\n            setTimeout(function() {\r\n                $notice.fadeOut();\r\n            }, 5000);\r\n        }\r\n    });\r\n\r\n})(jQuery);", "/**\r\n * AutoHub User Registration Live Validation\r\n * Provides real-time form validation for registration forms\r\n */\r\n\r\n(function($) {\r\n    'use strict';\r\n\r\n    // Validation patterns\r\n    const patterns = {\r\n        username: /^[a-zA-Z0-9_-]+$/,\r\n        email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n        phone: /^(\\+260|0)[0-9]{9}$/,\r\n        password: {\r\n            minLength: 8,\r\n            hasLowercase: /[a-z]/,\r\n            hasUppercase: /[A-Z]/,\r\n            hasNumber: /[0-9]/,\r\n            hasSpecial: /[!@#$%^&*(),.?\":{}|<>]/\r\n        }\r\n    };\r\n\r\n    // Validation messages\r\n    const messages = {\r\n        username: {\r\n            required: 'Username is required',\r\n            minLength: 'Username must be at least 3 characters long',\r\n            pattern: 'Username can only contain letters, numbers, underscores, and hyphens',\r\n            exists: 'This username is already taken'\r\n        },\r\n        email: {\r\n            required: 'Email address is required',\r\n            invalid: 'Please enter a valid email address',\r\n            exists: 'This email address is already registered'\r\n        },\r\n        phone: {\r\n            required: 'Phone number is required',\r\n            invalid: 'Please enter a valid Zambian phone number (e.g., +260 XXX XXX XXX)'\r\n        },\r\n        password: {\r\n            required: 'Password is required',\r\n            minLength: 'Password must be at least 8 characters long',\r\n            weak: 'Password should contain uppercase, lowercase, number, and special character'\r\n        },\r\n        confirmPassword: {\r\n            required: 'Please confirm your password',\r\n            mismatch: 'Passwords do not match'\r\n        },\r\n        businessName: {\r\n            required: 'Business name is required',\r\n            minLength: 'Business name must be at least 2 characters long'\r\n        },\r\n        firstName: {\r\n            required: 'First name is required',\r\n            minLength: 'First name must be at least 2 characters long'\r\n        },\r\n        lastName: {\r\n            required: 'Last name is required',\r\n            minLength: 'Last name must be at least 2 characters long'\r\n        },\r\n        corporateName: {\r\n            required: 'Corporate name is required',\r\n            minLength: 'Corporate name must be at least 2 characters long'\r\n        }\r\n    };\r\n\r\n    // Debounce function for API calls\r\n    function debounce(func, wait) {\r\n        let timeout;\r\n        return function executedFunction(...args) {\r\n            const later = () => {\r\n                clearTimeout(timeout);\r\n                func(...args);\r\n            };\r\n            clearTimeout(timeout);\r\n            timeout = setTimeout(later, wait);\r\n        };\r\n    }\r\n\r\n    // Show field feedback\r\n    function showFieldFeedback(field, message, type = 'error') {\r\n        const $field = $(field);\r\n        // Try to find feedback element as sibling first, then in parent container\r\n        let $feedback = $field.siblings('.field-feedback');\r\n        if ($feedback.length === 0) {\r\n            $feedback = $field.closest('.form-group').find('.field-feedback');\r\n        }\r\n        \r\n        // Remove existing classes\r\n        $field.removeClass('border-red-500 border-green-500 border-gray-300');\r\n        $feedback.removeClass('text-red-500 text-green-500 hidden');\r\n        \r\n        if (type === 'error') {\r\n            $field.addClass('border-red-500');\r\n            $feedback.addClass('text-red-500').text(message).removeClass('hidden');\r\n        } else if (type === 'success') {\r\n            $field.addClass('border-green-500');\r\n            $feedback.addClass('text-green-500').text(message).removeClass('hidden');\r\n        } else {\r\n            $field.addClass('border-gray-300');\r\n            $feedback.addClass('hidden');\r\n        }\r\n    }\r\n\r\n    // Clear field feedback\r\n    function clearFieldFeedback(field) {\r\n        const $field = $(field);\r\n        const $feedback = $field.siblings('.field-feedback');\r\n        \r\n        $field.removeClass('border-red-500 border-green-500').addClass('border-gray-300');\r\n        $feedback.addClass('hidden');\r\n    }\r\n\r\n    // Validate username\r\n    function validateUsername(username, callback) {\r\n        if (!username) {\r\n            callback(messages.username.required, 'error');\r\n            return;\r\n        }\r\n\r\n        if (username.length < 3) {\r\n            callback(messages.username.minLength, 'error');\r\n            return;\r\n        }\r\n\r\n        if (!patterns.username.test(username)) {\r\n            callback(messages.username.pattern, 'error');\r\n            return;\r\n        }\r\n\r\n        // Check if username exists\r\n        $.ajax({\r\n            url: autohub_reg_ajax.ajax_url,\r\n            type: 'POST',\r\n            data: {\r\n                action: 'autohub_check_username',\r\n                username: username,\r\n                nonce: autohub_reg_ajax.nonce\r\n            },\r\n            success: function(response) {\r\n                if (response.success) {\r\n                    if (response.data.available) {\r\n                        callback('Username is available', 'success');\r\n                    } else {\r\n                        callback(messages.username.exists, 'error');\r\n                    }\r\n                } else {\r\n                    callback(response.data.message || 'Error checking username', 'error');\r\n                }\r\n            },\r\n            error: function() {\r\n                callback('Error checking username', 'error');\r\n            }\r\n        });\r\n    }\r\n\r\n    // Validate email\r\n    function validateEmail(email, callback) {\r\n        if (!email) {\r\n            callback(messages.email.required, 'error');\r\n            return;\r\n        }\r\n\r\n        if (!patterns.email.test(email)) {\r\n            callback(messages.email.invalid, 'error');\r\n            return;\r\n        }\r\n\r\n        // Check if email exists\r\n        $.ajax({\r\n            url: autohub_reg_ajax.ajax_url,\r\n            type: 'POST',\r\n            data: {\r\n                action: 'autohub_check_email',\r\n                email: email,\r\n                nonce: autohub_reg_ajax.nonce\r\n            },\r\n            success: function(response) {\r\n                if (response.success) {\r\n                    if (response.data.available) {\r\n                        callback('Email is available', 'success');\r\n                    } else {\r\n                        callback(messages.email.exists, 'error');\r\n                    }\r\n                } else {\r\n                    callback(response.data.message || 'Error checking email', 'error');\r\n                }\r\n            },\r\n            error: function() {\r\n                callback('Error checking email', 'error');\r\n            }\r\n        });\r\n    }\r\n\r\n    // Validate phone number\r\n    function validatePhone(phone) {\r\n        if (!phone) {\r\n            return { valid: false, message: messages.phone.required };\r\n        }\r\n\r\n        // Clean phone number (remove spaces and dashes)\r\n        const cleanPhone = phone.replace(/[\\s-]/g, '');\r\n        \r\n        if (!patterns.phone.test(cleanPhone)) {\r\n            return { valid: false, message: messages.phone.invalid };\r\n        }\r\n\r\n        return { valid: true, message: 'Valid phone number' };\r\n    }\r\n\r\n    // Validate password\r\n    function validatePassword(password) {\r\n        if (!password) {\r\n            return { valid: false, message: messages.password.required, strength: 'none' };\r\n        }\r\n\r\n        if (password.length < patterns.password.minLength) {\r\n            return { valid: false, message: messages.password.minLength, strength: 'weak' };\r\n        }\r\n\r\n        let strength = 'weak';\r\n        let score = 0;\r\n        let missing = [];\r\n\r\n        // Check each requirement\r\n        if (patterns.password.hasLowercase.test(password)) {\r\n            score++;\r\n        } else {\r\n            missing.push('lowercase letter');\r\n        }\r\n\r\n        if (patterns.password.hasUppercase.test(password)) {\r\n            score++;\r\n        } else {\r\n            missing.push('uppercase letter');\r\n        }\r\n\r\n        if (patterns.password.hasNumber.test(password)) {\r\n            score++;\r\n        } else {\r\n            missing.push('number');\r\n        }\r\n\r\n        if (patterns.password.hasSpecial.test(password)) {\r\n            score++;\r\n        } else {\r\n            missing.push('special character');\r\n        }\r\n\r\n        // Determine strength\r\n        if (score >= 4) strength = 'strong';\r\n        else if (score >= 3) strength = 'medium';\r\n\r\n        // Require at least medium strength (3 out of 4 criteria)\r\n        const valid = score >= 3;\r\n        \r\n        let message;\r\n        if (valid) {\r\n            message = `Password strength: ${strength}`;\r\n        } else {\r\n            if (missing.length === 1) {\r\n                message = `Password needs: ${missing[0]}`;\r\n            } else if (missing.length === 2) {\r\n                message = `Password needs: ${missing.join(' and ')}`;\r\n            } else {\r\n                message = `Password needs: ${missing.slice(0, -1).join(', ')} and ${missing[missing.length - 1]}`;\r\n            }\r\n        }\r\n\r\n        return { valid, message, strength };\r\n    }\r\n\r\n    // Validate confirm password\r\n    function validateConfirmPassword(password, confirmPassword) {\r\n        if (!confirmPassword) {\r\n            return { valid: false, message: messages.confirmPassword.required };\r\n        }\r\n\r\n        if (password !== confirmPassword) {\r\n            return { valid: false, message: messages.confirmPassword.mismatch };\r\n        }\r\n\r\n        return { valid: true, message: 'Passwords match' };\r\n    }\r\n\r\n    // Validate required text field\r\n    function validateRequiredText(value, fieldName, minLength = 2) {\r\n        // Check if fieldName exists in messages object\r\n        if (!messages[fieldName]) {\r\n            return { valid: false, message: 'This field is required' };\r\n        }\r\n\r\n        if (!value || value.trim() === '') {\r\n            return { valid: false, message: messages[fieldName].required };\r\n        }\r\n\r\n        if (value.trim().length < minLength) {\r\n            return { valid: false, message: messages[fieldName].minLength };\r\n        }\r\n\r\n        return { valid: true, message: '' };\r\n    }\r\n\r\n    // Initialize consumer registration form with live validation\r\n    function initConsumerRegistration() {\r\n        let $form = $('#autohub-registration-form');\r\n        \r\n        // Fallback: try to find form by other means\r\n        if (!$form.length) {\r\n            $form = $('form').filter(function() {\r\n                return $(this).find('input[name=\"consumer_type\"]').length > 0;\r\n            });\r\n        }\r\n        \r\n        if (!$form.length) return;\r\n\r\n        // Consumer type toggle functionality\r\n        $form.find('input[name=\"consumer_type\"]').on('change', function() {\r\n            const consumerType = $(this).val();\r\n            const $individualFields = $('#individual-fields');\r\n            const $corporateFields = $('#corporate-fields');\r\n            \r\n            if (consumerType === 'corporate') {\r\n                $individualFields.addClass('hidden');\r\n                $corporateFields.removeClass('hidden');\r\n                \r\n                // Enable corporate fields and make them required\r\n                $corporateFields.find('input').prop('disabled', false).attr('required', true);\r\n                // Disable individual fields and remove required attribute\r\n                $individualFields.find('input').prop('disabled', true).removeAttr('required');\r\n                \r\n                // Clear any validation errors from hidden fields\r\n                $individualFields.find('input').removeClass('border-red-500 border-green-500').addClass('border-gray-300');\r\n                $individualFields.find('.field-feedback').addClass('hidden');\r\n            } else {\r\n                $individualFields.removeClass('hidden');\r\n                $corporateFields.addClass('hidden');\r\n                \r\n                // Enable individual fields and make them required\r\n                $individualFields.find('input').prop('disabled', false).attr('required', true);\r\n                // Disable corporate fields and remove required attribute\r\n                $corporateFields.find('input').prop('disabled', true).removeAttr('required');\r\n                \r\n                // Clear any validation errors from hidden fields\r\n                $corporateFields.find('input').removeClass('border-red-500 border-green-500').addClass('border-gray-300');\r\n                $corporateFields.find('.field-feedback').addClass('hidden');\r\n            }\r\n        });\r\n\r\n        // Initialize the form state\r\n        $form.find('input[name=\"consumer_type\"]:checked').trigger('change');\r\n\r\n        // Username validation with debounce\r\n        const debouncedUsernameCheck = debounce(function(username, field) {\r\n            validateUsername(username, function(message, type) {\r\n                showFieldFeedback(field, message, type);\r\n            });\r\n        }, 500);\r\n\r\n        // Note: Password validation is handled by initBusinessRegistration() for all forms\r\n\r\n        $form.find('#username').on('input blur', function() {\r\n            const username = $(this).val().trim();\r\n            const field = this;\r\n            \r\n            if (username) {\r\n                debouncedUsernameCheck(username, field);\r\n            } else {\r\n                clearFieldFeedback(field);\r\n            }\r\n        });\r\n\r\n        // Email validation with debounce\r\n        const debouncedEmailCheck = debounce(function(email, field) {\r\n            validateEmail(email, function(message, type) {\r\n                showFieldFeedback(field, message, type);\r\n            });\r\n        }, 500);\r\n\r\n        $form.find('#email').on('input blur', function() {\r\n            const email = $(this).val().trim();\r\n            const field = this;\r\n            \r\n            if (email) {\r\n                debouncedEmailCheck(email, field);\r\n            } else {\r\n                clearFieldFeedback(field);\r\n            }\r\n        });\r\n\r\n        // Phone validation\r\n        $form.find('#phone').on('input blur', function() {\r\n            const phone = $(this).val().trim();\r\n            const result = validatePhone(phone);\r\n            \r\n            if (phone) {\r\n                showFieldFeedback(this, result.message, result.valid ? 'success' : 'error');\r\n            } else {\r\n                clearFieldFeedback(this);\r\n            }\r\n        });\r\n\r\n        // Note: Password validation is handled by initBusinessRegistration() for all forms\r\n\r\n        // Note: Name field validation is handled by initBusinessRegistration() for all forms\r\n    }\r\n\r\n    // Initialize validation for all registration forms (renamed for clarity)\r\n    function initBusinessRegistration() {\r\n        let $form = $('#autohub-business-owner-registration-form, #autohub-registration-form');\r\n        \r\n        // Fallback: try to find ANY form with password fields\r\n        if (!$form.length) {\r\n            $form = $('form').filter(function() {\r\n                return $(this).find('input[name=\"password\"], #consumer_password, #bo_password').length > 0;\r\n            });\r\n        }\r\n        \r\n        if (!$form.length) return;\r\n\r\n        // Username validation with debounce (supports both business forms)\r\n        const debouncedUsernameCheck = debounce(function(username, field) {\r\n            validateUsername(username, function(message, type) {\r\n                showFieldFeedback(field, message, type);\r\n            });\r\n        }, 500);\r\n\r\n        $form.find('#consumer_username, #bo_username').on('input blur', function() {\r\n            const username = $(this).val().trim();\r\n            const field = this;\r\n            \r\n            if (username) {\r\n                debouncedUsernameCheck(username, field);\r\n            } else {\r\n                // Show required message when field is empty and loses focus\r\n                showFieldFeedback(field, 'Username is required', 'error');\r\n            }\r\n        });\r\n\r\n        // Email validation with debounce (supports both business forms)\r\n        const debouncedEmailCheck = debounce(function(email, field) {\r\n            validateEmail(email, function(message, type) {\r\n                showFieldFeedback(field, message, type);\r\n            });\r\n        }, 500);\r\n\r\n        $form.find('#email, #bo_email').on('input blur', function() {\r\n            const email = $(this).val().trim();\r\n            const field = this;\r\n            \r\n            if (email) {\r\n                debouncedEmailCheck(email, field);\r\n            } else {\r\n                // Show required message when field is empty and loses focus\r\n                showFieldFeedback(field, 'Email address is required', 'error');\r\n            }\r\n        });\r\n\r\n        // Phone validation for all business phone fields\r\n        $form.find('#phone, #bo_phone').on('input blur', function() {\r\n            const phone = $(this).val().trim();\r\n            const result = validatePhone(phone);\r\n            \r\n            if (phone) {\r\n                showFieldFeedback(this, result.message, result.valid ? 'success' : 'error');\r\n            } else {\r\n                // Show required message when field is empty and loses focus\r\n                showFieldFeedback(this, 'Phone number is required', 'error');\r\n            }\r\n        });\r\n\r\n        // Password validation with real-time feedback (supports all forms)\r\n        const $passwordFields = $form.find('input[name=\"password\"], #consumer_password, #bo_password');\r\n\r\n        \r\n\r\n        \r\n        $passwordFields.on('focus', function() {\r\n            // Focus handler\r\n        });\r\n        \r\n        $passwordFields.on('input keyup', function() {\r\n\r\n            const password = $(this).val();\r\n\r\n            const passwordValidation = validatePassword(password);\r\n\r\n            const $strengthIndicator = $(this).closest('.form-group').find('.password-strength-indicator');\r\n            const $strengthText = $strengthIndicator.find('.strength-text');\r\n\r\n            const $strengthBars = $strengthIndicator.find('.strength-bar');\r\n            \r\n            // Update strength indicator\r\n            $strengthIndicator.removeClass('strength-weak strength-medium strength-strong');\r\n            if (passwordValidation.strength !== 'none') {\r\n                $strengthIndicator.addClass('strength-' + passwordValidation.strength);\r\n                $strengthText.text(passwordValidation.strength.charAt(0).toUpperCase() + passwordValidation.strength.slice(1));\r\n                \r\n                // Update strength bars\r\n                $strengthBars.removeClass('bg-red-500 bg-orange-500 bg-green-500').addClass('bg-gray-200');\r\n                if (passwordValidation.strength === 'weak') {\r\n                    $strengthBars.eq(0).removeClass('bg-gray-200').addClass('bg-red-500');\r\n                } else if (passwordValidation.strength === 'medium') {\r\n                    $strengthBars.eq(0).removeClass('bg-gray-200').addClass('bg-orange-500');\r\n                    $strengthBars.eq(1).removeClass('bg-gray-200').addClass('bg-orange-500');\r\n                } else if (passwordValidation.strength === 'strong') {\r\n                    $strengthBars.removeClass('bg-gray-200').addClass('bg-green-500');\r\n                }\r\n            } else {\r\n                $strengthText.text('');\r\n                $strengthBars.removeClass('bg-red-500 bg-orange-500 bg-green-500').addClass('bg-gray-200');\r\n            }\r\n            \r\n            // Show validation feedback\r\n            if (password.length > 0) {\r\n                showFieldFeedback(this, passwordValidation.message, passwordValidation.valid ? 'success' : 'error');\r\n            } else {\r\n                clearFieldFeedback(this);\r\n            }\r\n\r\n            // Also validate confirm password if it has a value\r\n            const confirmPassword = $form.find('input[name=\"confirm_password\"], #consumer_confirm_password, #bo_confirm_password').val();\r\n            if (confirmPassword) {\r\n                const confirmResult = validateConfirmPassword(password, confirmPassword);\r\n                const confirmField = $form.find('input[name=\"confirm_password\"], #consumer_confirm_password, #bo_confirm_password')[0];\r\n                if (confirmField) {\r\n                    showFieldFeedback(confirmField, confirmResult.message, confirmResult.valid ? 'success' : 'error');\r\n                }\r\n            }\r\n        });\r\n\r\n        // Password validation on blur (when user leaves field)\r\n        $form.find('input[name=\"password\"], #consumer_password, #bo_password').on('blur', function() {\r\n            const password = $(this).val();\r\n            if (password.length === 0) {\r\n                showFieldFeedback(this, 'Password is required', 'error');\r\n            }\r\n        });\r\n\r\n        // Confirm password validation (supports all forms)\r\n        $form.find('input[name=\"confirm_password\"], #consumer_confirm_password, #bo_confirm_password').on('input keyup', function() {\r\n            const password = $form.find('input[name=\"password\"], #consumer_password, #bo_password').val();\r\n            const confirmPassword = $(this).val();\r\n            \r\n            if (confirmPassword.length > 0) {\r\n                const confirmValidation = validateConfirmPassword(password, confirmPassword);\r\n                showFieldFeedback(this, confirmValidation.message, confirmValidation.valid ? 'success' : 'error');\r\n            } else {\r\n                clearFieldFeedback(this);\r\n            }\r\n        });\r\n\r\n        // Confirm password validation on blur (when user leaves field)\r\n        $form.find('input[name=\"confirm_password\"], #consumer_confirm_password, #bo_confirm_password').on('blur', function() {\r\n            const confirmPassword = $(this).val();\r\n            if (confirmPassword.length === 0) {\r\n                showFieldFeedback(this, 'Please confirm your password', 'error');\r\n            }\r\n        });\r\n\r\n        // Name fields validation (supports all forms)\r\n        $form.find('#first_name, #last_name, #corporate_name, #contact_first_name, #contact_last_name, #bo_first_name, #bo_last_name').on('input blur', function() {\r\n            const value = $(this).val().trim();\r\n            const fieldId = $(this).attr('id');\r\n            let fieldName = 'firstName';\r\n            \r\n            // Determine field type\r\n            if (fieldId === 'last_name' || fieldId === 'contact_last_name' || fieldId === 'bo_last_name') {\r\n                fieldName = 'lastName';\r\n            } else if (fieldId === 'corporate_name') {\r\n                fieldName = 'corporateName';\r\n            }\r\n            \r\n            const result = validateRequiredText(value, fieldName);\r\n            \r\n            if (value) {\r\n                showFieldFeedback(this, result.message, result.valid ? 'success' : 'error');\r\n            } else {\r\n                // Show required message when field is empty and loses focus\r\n                let message = 'First name is required';\r\n                if (fieldId === 'last_name' || fieldId === 'contact_last_name' || fieldId === 'bo_last_name') {\r\n                    message = 'Last name is required';\r\n                } else if (fieldId === 'corporate_name') {\r\n                    message = 'Corporate name is required';\r\n                }\r\n                showFieldFeedback(this, message, 'error');\r\n            }\r\n        });\r\n\r\n        // Business name validation\r\n        $form.find('#business_name').on('input blur', function() {\r\n            const value = $(this).val().trim();\r\n            const result = validateRequiredText(value, 'businessName');\r\n            \r\n            if (value) {\r\n                showFieldFeedback(this, result.message, result.valid ? 'success' : 'error');\r\n            } else {\r\n                clearFieldFeedback(this);\r\n            }\r\n        });\r\n    }\r\n\r\n    // Update password strength indicator\r\n    function updatePasswordStrength(strength) {\r\n        const $indicator = $('.password-strength-indicator');\r\n        if (!$indicator.length) return;\r\n\r\n        $indicator.removeClass('strength-none strength-weak strength-medium strength-strong');\r\n        $indicator.addClass('strength-' + strength);\r\n\r\n        const strengthText = {\r\n            'none': '',\r\n            'weak': 'Weak',\r\n            'medium': 'Medium',\r\n            'strong': 'Strong'\r\n        };\r\n\r\n        $indicator.find('.strength-text').text(strengthText[strength]);\r\n    }\r\n\r\n    // Password toggle functionality\r\n    function initPasswordToggle() {\r\n        $(document).on('click', '.password-toggle', function() {\r\n            const $button = $(this);\r\n            const targetId = $button.data('target');\r\n            const $input = $('#' + targetId);\r\n            const $eyeOpen = $button.find('.eye-open');\r\n            const $eyeClosed = $button.find('.eye-closed');\r\n\r\n            if ($input.attr('type') === 'password') {\r\n                $input.attr('type', 'text');\r\n                $eyeOpen.addClass('hidden');\r\n                $eyeClosed.removeClass('hidden');\r\n            } else {\r\n                $input.attr('type', 'password');\r\n                $eyeOpen.removeClass('hidden');\r\n                $eyeClosed.addClass('hidden');\r\n            }\r\n        });\r\n    }\r\n\r\n    // Form submission validation\r\n    function validateFormBeforeSubmit($form) {\r\n        let isValid = true;\r\n        const errors = [];\r\n\r\n        // Check all required fields\r\n        $form.find('[required]').each(function() {\r\n            const $field = $(this);\r\n            const value = $field.val().trim();\r\n\r\n            if (!value) {\r\n                const label = $field.closest('.form-group').find('label').text().replace('*', '').trim();\r\n                errors.push(label + ' is required');\r\n                showFieldFeedback(this, 'This field is required', 'error');\r\n                isValid = false;\r\n            }\r\n        });\r\n\r\n        // Validate password strength specifically\r\n        const $passwordField = $form.find('input[name=\"password\"]');\r\n        if ($passwordField.length) {\r\n            const password = $passwordField.val();\r\n            const passwordValidation = validatePassword(password);\r\n            \r\n            if (!passwordValidation.valid) {\r\n                errors.push('Password does not meet requirements');\r\n                showFieldFeedback($passwordField[0], passwordValidation.message, 'error');\r\n                isValid = false;\r\n            }\r\n        }\r\n\r\n        // Validate confirm password\r\n        const $confirmPasswordField = $form.find('input[name=\"confirm_password\"]');\r\n        if ($confirmPasswordField.length && $passwordField.length) {\r\n            const password = $passwordField.val();\r\n            const confirmPassword = $confirmPasswordField.val();\r\n            const confirmValidation = validateConfirmPassword(password, confirmPassword);\r\n            \r\n            if (!confirmValidation.valid) {\r\n                errors.push('Password confirmation does not match');\r\n                showFieldFeedback($confirmPasswordField[0], confirmValidation.message, 'error');\r\n                isValid = false;\r\n            }\r\n        }\r\n\r\n        // Check for existing validation errors\r\n        $form.find('.border-red-500').each(function() {\r\n            isValid = false;\r\n        });\r\n\r\n        return { isValid, errors };\r\n    }\r\n\r\n    // Load plan costs for business registration\r\n    function loadPlanCosts() {\r\n        // Check if we have the plan cost display elements on the page\r\n        const $planCostDisplay = $('#plan-cost-display');\r\n        const $registrationTypeInputs = $('input[name=\"registration_type\"]');\r\n        \r\n        if ($planCostDisplay.length === 0 || $registrationTypeInputs.length === 0) {\r\n            return; // No plan elements found, skip loading\r\n        }\r\n        \r\n        // Check if AJAX object is available\r\n        if (typeof autohub_reg_ajax === 'undefined') {\r\n            console.warn('AutoHub Registration: AJAX object not available for plan costs');\r\n            return;\r\n        }\r\n        \r\n        // Store plan data globally for use when radio buttons change\r\n        let planCosts = {};\r\n        \r\n        // Make AJAX request to get plan costs\r\n        $.ajax({\r\n            url: autohub_reg_ajax.ajax_url,\r\n            type: 'POST',\r\n            data: {\r\n                action: 'autohub_get_plan_costs',\r\n                nonce: autohub_reg_ajax.nonce\r\n            },\r\n            success: function(response) {\r\n                if (response.success && response.data) {\r\n                    planCosts = response.data;\r\n                    \r\n                    // Update display if a radio button is already selected\r\n                    const $selectedInput = $registrationTypeInputs.filter(':checked');\r\n                    if ($selectedInput.length) {\r\n                        showPlanCost($selectedInput.val());\r\n                    }\r\n                } else {\r\n                    console.error('AutoHub Registration: Failed to load plan costs:', response);\r\n                }\r\n            },\r\n            error: function(xhr, status, error) {\r\n                console.error('AutoHub Registration: Error loading plan costs:', error);\r\n            }\r\n        });\r\n        \r\n        // Function to show plan cost\r\n        function showPlanCost(type) {\r\n            const planPricingContent = document.getElementById('plan-pricing-content');\r\n            const planKey = type === 'auto_shop' ? 'auto_shop' : 'vehicle_professional';\r\n            \r\n            if (planCosts[planKey]) {\r\n                // Use innerHTML to properly render the HTML content\r\n                planPricingContent.innerHTML = planCosts[planKey];\r\n                $planCostDisplay.removeClass('hidden');\r\n            } else {\r\n                planPricingContent.innerHTML = '<div class=\"text-gray-600\">Loading pricing...</div>';\r\n                $planCostDisplay.removeClass('hidden');\r\n            }\r\n        }\r\n        \r\n        // Handle registration type selection\r\n        $registrationTypeInputs.on('change', function() {\r\n            if (this.checked) {\r\n                showPlanCost(this.value);\r\n                \r\n                // Update visual selection state\r\n                $('.registration-type-option').removeClass('border-primary bg-primary-50').addClass('border-gray-300');\r\n                \r\n                const $selectedOption = $(this).closest('.registration-type-option');\r\n                $selectedOption.removeClass('border-gray-300').addClass('border-primary bg-primary-50');\r\n            }\r\n        });\r\n    }\r\n\r\n    // Handle resend verification email functionality\r\n    function initResendVerification() {\r\n        // Handle resend verification button click\r\n        $(document).on('click', '#resend-verification-btn', function(e) {\r\n            e.preventDefault();\r\n            \r\n            const $btn = $(this);\r\n            const $messageDiv = $('#resend-verification-message');\r\n            const userEmail = $btn.data('user-email');\r\n            \r\n            if (!userEmail) {\r\n\r\n                $messageDiv.html('<div style=\"color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;\">Error: No email address found</div>').show();\r\n                return;\r\n            }\r\n            \r\n            if (typeof autohub_reg_ajax === 'undefined') {\r\n\r\n                $messageDiv.html('<div style=\"color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;\">Error: AJAX not configured</div>').show();\r\n                return;\r\n            }\r\n            \r\n            // Show loading state\r\n            $btn.prop('disabled', true).text('Sending...');\r\n            $messageDiv.hide();\r\n            \r\n            // Make AJAX request\r\n            $.ajax({\r\n                url: autohub_reg_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: {\r\n                    action: 'autohub_resend_verification',\r\n                    email: userEmail,\r\n                    nonce: autohub_reg_ajax.nonce\r\n                },\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        $messageDiv.html('<div style=\"color: #2e7d32; padding: 10px; background: #e8f5e8; border-radius: 4px;\">' + response.data.message + '</div>').show();\r\n                        $btn.text('Email Sent!');\r\n                        \r\n                        // Re-enable button after 30 seconds\r\n                        setTimeout(function() {\r\n                            $btn.prop('disabled', false).text('Resend Verification Email');\r\n                        }, 30000);\r\n                    } else {\r\n                        $messageDiv.html('<div style=\"color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;\">' + (response.data.message || 'Failed to send verification email') + '</div>').show();\r\n                        $btn.prop('disabled', false).text('Resend Verification Email');\r\n                    }\r\n                },\r\n                error: function(xhr, status, error) {\r\n                    console.error('AutoHub: AJAX error:', error);\r\n                    console.error('AutoHub: XHR response:', xhr.responseText);\r\n                    $messageDiv.html('<div style=\"color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;\">Connection error. Please try again.</div>').show();\r\n                    $btn.prop('disabled', false).text('Resend Verification Email');\r\n                }\r\n            });\r\n        });\r\n    }\r\n\r\n    // Initialize all functionality\r\n    $(document).ready(function() {\r\n        // Try to initialize forms\r\n        initConsumerRegistration();\r\n        initBusinessRegistration();\r\n        initPasswordToggle();\r\n        initResendVerification();\r\n        initVerificationStatus();\r\n        \r\n        // Load plan costs for business registration form\r\n        loadPlanCosts();\r\n        \r\n        // Fallback: Initialize validation on any form with registration fields\r\n        const $anyRegForm = $('form').filter(function() {\r\n            const hasUsername = $(this).find('input[name=\"username\"]').length > 0;\r\n            const hasEmail = $(this).find('input[name=\"email\"], input[name=\"business_email\"]').length > 0;\r\n            const hasPassword = $(this).find('input[name=\"password\"]').length > 0;\r\n            return hasUsername && hasEmail && hasPassword;\r\n        });\r\n\r\n        // Handle consumer registration form submission with AJAX\r\n        $('#autohub-registration-form').on('submit', function(e) {\r\n            e.preventDefault();\r\n            \r\n            const $form = $(this);\r\n            const $submitBtn = $form.find('button[type=\"submit\"]');\r\n            const $submitText = $submitBtn.find('.btn-text');\r\n            const $loadingText = $submitBtn.find('.btn-loading');\r\n            const $messagesDiv = $('#registration-messages');\r\n            \r\n            // Validate form before submission\r\n            const validation = validateFormBeforeSubmit($form);\r\n            \r\n            if (!validation.isValid) {\r\n                // Show specific error messages\r\n                let errorHtml = '<div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">';\r\n                errorHtml += '<div class=\"font-medium mb-2\">Please fix the following errors:</div>';\r\n                errorHtml += '<ul class=\"list-disc list-inside space-y-1\">';\r\n                \r\n                validation.errors.forEach(function(error) {\r\n                    errorHtml += '<li>' + error + '</li>';\r\n                });\r\n                \r\n                errorHtml += '</ul></div>';\r\n                $messagesDiv.html(errorHtml);\r\n                $messagesDiv.removeClass('hidden');\r\n                \r\n                // Scroll to first error\r\n                const $firstError = $form.find('.border-red-500').first();\r\n                if ($firstError.length) {\r\n                    $firstError[0].scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n                    $firstError.focus();\r\n                }\r\n                return;\r\n            }\r\n            \r\n            // Show loading state\r\n            $submitBtn.prop('disabled', true);\r\n            $submitText.addClass('hidden');\r\n            $loadingText.removeClass('hidden');\r\n            $messagesDiv.addClass('hidden');\r\n            \r\n            // Prepare form data\r\n            const formData = new FormData($form[0]);\r\n            formData.append('action', 'autohub_register_user');\r\n            \r\n            // Submit form via AJAX\r\n            $.ajax({\r\n                url: autohub_reg_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: formData,\r\n                processData: false,\r\n                contentType: false,\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        // Show success message\r\n                        $messagesDiv.html('<div class=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\">' + \r\n                            '<div class=\"font-medium mb-2\">Registration Successful!</div>' +\r\n                            '<p>' + response.data.message + '</p>' +\r\n                            '</div>');\r\n                        $messagesDiv.removeClass('hidden');\r\n                        \r\n                        // Reset form\r\n                        $form[0].reset();\r\n                        \r\n                        // Clear all field feedback\r\n                        $form.find('.field-feedback').addClass('hidden');\r\n                        $form.find('input').removeClass('border-red-500 border-green-500').addClass('border-gray-300');\r\n                        \r\n                        // Reset password strength indicator\r\n                        $form.find('.password-strength-indicator').removeClass('strength-weak strength-medium strength-strong');\r\n                        $form.find('.strength-text').text('');\r\n                        $form.find('.strength-bar').removeClass('bg-red-500 bg-orange-500 bg-green-500').addClass('bg-gray-200');\r\n                        \r\n                        // Reset consumer type to individual and show/hide fields accordingly\r\n                        $form.find('input[name=\"consumer_type\"][value=\"individual\"]').prop('checked', true);\r\n                        $('#individual-fields').removeClass('hidden');\r\n                        $('#corporate-fields').addClass('hidden');\r\n                        \r\n                        // Scroll to success message\r\n                        $messagesDiv[0].scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n                        \r\n                    } else {\r\n                        // Show error message\r\n                        $messagesDiv.html('<div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">' + \r\n                            '<div class=\"font-medium mb-2\">Registration Failed</div>' +\r\n                            '<p>' + (response.data.message || 'An error occurred. Please try again.') + '</p>' +\r\n                            '</div>');\r\n                        $messagesDiv.removeClass('hidden');\r\n                    }\r\n                },\r\n                error: function(xhr, status, error) {\r\n                    console.error('Consumer registration error:', error);\r\n                    // Show error message\r\n                    let errorMessage = 'Connection Error: ';\r\n                    if (xhr.status === 500) {\r\n                        errorMessage += 'Server error occurred. Please try again or contact support.';\r\n                    } else if (xhr.status === 0) {\r\n                        errorMessage += 'Unable to connect to the server. Please check your internet connection and try again.';\r\n                    } else {\r\n                        errorMessage += 'An unexpected error occurred. Please try again.';\r\n                    }\r\n                    \r\n                    $messagesDiv.html('<div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">' + \r\n                        '<div class=\"font-medium mb-2\">Registration Failed</div>' +\r\n                        '<p>' + errorMessage + '</p>' +\r\n                        '</div>');\r\n                    $messagesDiv.removeClass('hidden');\r\n                },\r\n                complete: function() {\r\n                    // Reset button state\r\n                    $submitBtn.prop('disabled', false);\r\n                    $submitText.removeClass('hidden');\r\n                    $loadingText.addClass('hidden');\r\n                }\r\n            });\r\n        });\r\n\r\n        // Handle business owner registration form submission with AJAX\r\n        $('#autohub-business-owner-registration-form').on('submit', function(e) {\r\n            e.preventDefault();\r\n            \r\n            const $form = $(this);\r\n            const $submitBtn = $form.find('button[type=\"submit\"]');\r\n            const $submitText = $submitBtn.find('.submit-text');\r\n            const $loadingText = $submitBtn.find('.loading-text');\r\n            const $messagesDiv = $('#form-messages');\r\n            \r\n            // Validate form before submission\r\n            const validation = validateFormBeforeSubmit($form);\r\n            \r\n            if (!validation.isValid) {\r\n                // Show specific error messages\r\n                let errorHtml = '<div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">';\r\n                errorHtml += '<div class=\"font-medium mb-2\">Please fix the following errors:</div>';\r\n                errorHtml += '<ul class=\"list-disc list-inside space-y-1\">';\r\n                \r\n                validation.errors.forEach(function(error) {\r\n                    errorHtml += '<li>' + error + '</li>';\r\n                });\r\n                \r\n                errorHtml += '</ul></div>';\r\n                $messagesDiv.html(errorHtml);\r\n                $messagesDiv.removeClass('hidden');\r\n                \r\n                // Scroll to first error\r\n                const $firstError = $form.find('.border-red-500').first();\r\n                if ($firstError.length) {\r\n                    $firstError[0].scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n                    $firstError.focus();\r\n                }\r\n                return;\r\n            }\r\n            \r\n            // Show loading state\r\n            $submitBtn.prop('disabled', true);\r\n            $submitText.addClass('hidden');\r\n            $loadingText.removeClass('hidden');\r\n            $messagesDiv.addClass('hidden');\r\n            \r\n            // Prepare form data\r\n            const formData = new FormData($form[0]);\r\n            formData.append('action', 'autohub_register_business_owner');\r\n            \r\n            // Submit form via AJAX\r\n            $.ajax({\r\n                url: autohub_reg_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: formData,\r\n                processData: false,\r\n                contentType: false,\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        // Show success message\r\n                        $messagesDiv.html('<div class=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\">' + \r\n                            '<div class=\"font-medium mb-2\">Registration Successful!</div>' +\r\n                            '<p>' + response.data.message + '</p>' +\r\n                            '</div>');\r\n                        $messagesDiv.removeClass('hidden');\r\n                        \r\n                        // Reset form\r\n                        $form[0].reset();\r\n                        \r\n                        // Clear all field feedback\r\n                        $form.find('.field-feedback').addClass('hidden');\r\n                        $form.find('input').removeClass('border-red-500 border-green-500').addClass('border-gray-300');\r\n                        \r\n                        // Reset password strength indicator\r\n                        $form.find('.password-strength-indicator').removeClass('strength-weak strength-medium strength-strong');\r\n                        $form.find('.strength-text').text('');\r\n                        $form.find('.strength-bar').removeClass('bg-red-500 bg-orange-500 bg-green-500').addClass('bg-gray-200');\r\n                        \r\n                        // Scroll to success message\r\n                        $messagesDiv[0].scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n                        \r\n                    } else {\r\n                        // Show error message\r\n                        $messagesDiv.html('<div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">' + \r\n                            '<div class=\"font-medium mb-2\">Registration Failed</div>' +\r\n                            '<p>' + (response.data.message || 'An error occurred. Please try again.') + '</p>' +\r\n                            '</div>');\r\n                        $messagesDiv.removeClass('hidden');\r\n                    }\r\n                },\r\n                error: function() {\r\n                    // Show error message\r\n                    $messagesDiv.html('<div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">' + \r\n                        '<div class=\"font-medium mb-2\">Connection Error</div>' +\r\n                        '<p>Unable to connect to the server. Please check your internet connection and try again.</p>' +\r\n                        '</div>');\r\n                    $messagesDiv.removeClass('hidden');\r\n                },\r\n                complete: function() {\r\n                    // Reset button state\r\n                    $submitBtn.prop('disabled', false);\r\n                    $submitText.removeClass('hidden');\r\n                    $loadingText.addClass('hidden');\r\n                }\r\n            });\r\n        });\r\n\r\n        // Form submission validation - try specific IDs first, then fallback to any registration form\r\n        let $submissionForms = $('#autohub-registration-form, #autohub-business-registration-form');\r\n        if (!$submissionForms.length) {\r\n            $submissionForms = $('form').filter(function() {\r\n                const hasUsername = $(this).find('input[name=\"username\"]').length > 0;\r\n                const hasEmail = $(this).find('input[name=\"email\"], input[name=\"business_email\"]').length > 0;\r\n                const hasPassword = $(this).find('input[name=\"password\"]').length > 0;\r\n                const formId = $(this).attr('id');\r\n                // Exclude business owner form as it has its own handler\r\n                return hasUsername && hasPassword && formId !== 'autohub-business-owner-registration-form';\r\n            });\r\n        }\r\n        \r\n        $submissionForms.on('submit', function(e) {\r\n            const validation = validateFormBeforeSubmit($(this));\r\n            \r\n            if (!validation.isValid) {\r\n                e.preventDefault();\r\n                \r\n                // Show specific error messages\r\n                const $messagesDiv = $(this).find('[id$=\"-messages\"]');\r\n                if ($messagesDiv.length) {\r\n                    let errorHtml = '<div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">';\r\n                    errorHtml += '<div class=\"font-medium mb-2\">Please fix the following errors:</div>';\r\n                    errorHtml += '<ul class=\"list-disc list-inside space-y-1\">';\r\n                    \r\n                    validation.errors.forEach(function(error) {\r\n                        errorHtml += '<li>' + error + '</li>';\r\n                    });\r\n                    \r\n                    errorHtml += '</ul></div>';\r\n                    $messagesDiv.html(errorHtml);\r\n                    $messagesDiv.removeClass('hidden');\r\n                }\r\n\r\n                // Scroll to first error\r\n                const $firstError = $(this).find('.border-red-500').first();\r\n                if ($firstError.length) {\r\n                    $firstError[0].scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n                    $firstError.focus();\r\n                }\r\n            }\r\n        });\r\n    });\r\n\r\n    // Initialize verification status functionality\r\n    function initVerificationStatus() {\r\n        // Check if verification status container exists (try multiple selectors)\r\n        const $container = $('.autohub-verification-status');\r\n        const $statusDisplay = $('#status-display');\r\n        const $actionButtons = $('#action-buttons');\r\n        \r\n        if (!$container.length && !$statusDisplay.length) {\r\n            return;\r\n        }\r\n\r\n        // Get URL parameters\r\n        const urlParams = new URLSearchParams(window.location.search);\r\n        const email = urlParams.get('email');\r\n        const token = urlParams.get('token');\r\n        \r\n        // Check for error parameter\r\n        const errorParam = urlParams.get('error');\r\n        if (errorParam === 'invalid_link') {\r\n            showError('Invalid or expired verification link. Please request a new verification email.');\r\n            return;\r\n        }\r\n        \r\n        // Check if we have the required parameters\r\n        if (!email) {\r\n            showError('Missing email parameter. Please use the link from your verification email or enter your email below to check status.');\r\n            // Show a simple email input form for manual status check\r\n            showEmailInputForm();\r\n            return;\r\n        }\r\n        \r\n        // Check verification status\r\n        checkVerificationStatus();\r\n        \r\n        function checkVerificationStatus() {\r\n            $.ajax({\r\n                url: autohub_reg_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: {\r\n                    action: 'autohub_check_verification_status',\r\n                    email: email,\r\n                    token: token || ''\r\n                },\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        displayStatus(response.data);\r\n                    } else {\r\n                        showError(response.data.message || 'Failed to check verification status');\r\n                    }\r\n                },\r\n                error: function() {\r\n                    showError('Connection error. Please try again.');\r\n                }\r\n            });\r\n        }\r\n        \r\n        function displayStatus(statusData) {\r\n            const $statusDisplay = $('#status-display');\r\n            const $actionButtons = $('#action-buttons');\r\n            \r\n            let statusHtml = '';\r\n            let statusClass = '';\r\n            \r\n            switch (statusData.status) {\r\n                case 'approved':\r\n                    statusClass = 'bg-green-100 border-green-400 text-green-700';\r\n                    statusHtml = `\r\n                        <div class=\"flex items-center justify-center mb-4\">\r\n                            <svg class=\"h-16 w-16 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                            </svg>\r\n                        </div>\r\n                        <h2 class=\"text-2xl font-bold text-green-700 mb-2\">Account Activated!</h2>\r\n                        <p class=\"text-green-600\">${statusData.message}</p>\r\n                    `;\r\n                    $('#login-section').removeClass('hidden');\r\n                    break;\r\n                    \r\n                case 'pending_approval':\r\n                    statusClass = 'bg-yellow-100 border-yellow-400 text-yellow-700';\r\n                    statusHtml = `\r\n                        <div class=\"flex items-center justify-center mb-4\">\r\n                            <svg class=\"h-16 w-16 text-yellow-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                            </svg>\r\n                        </div>\r\n                        <h2 class=\"text-2xl font-bold text-yellow-700 mb-2\">Awaiting Approval</h2>\r\n                        <p class=\"text-yellow-600\">${statusData.message}</p>\r\n                    `;\r\n                    break;\r\n                    \r\n                case 'verified':\r\n                    statusClass = 'bg-blue-100 border-blue-400 text-blue-700';\r\n                    statusHtml = `\r\n                        <div class=\"flex items-center justify-center mb-4\">\r\n                            <svg class=\"h-16 w-16 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                            </svg>\r\n                        </div>\r\n                        <h2 class=\"text-2xl font-bold text-blue-700 mb-2\">Email Verified</h2>\r\n                        <p class=\"text-blue-600\">${statusData.message}</p>\r\n                    `;\r\n                    $('#login-section').removeClass('hidden');\r\n                    break;\r\n                    \r\n                case 'unverified':\r\n                    statusClass = 'bg-red-100 border-red-400 text-red-700';\r\n                    statusHtml = `\r\n                        <div class=\"flex items-center justify-center mb-4\">\r\n                            <svg class=\"h-16 w-16 text-red-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                            </svg>\r\n                        </div>\r\n                        <h2 class=\"text-2xl font-bold text-red-700 mb-2\">Email Not Verified</h2>\r\n                        <p class=\"text-red-600\">${statusData.message}</p>\r\n                    `;\r\n                    \r\n                    // Show resend and change email options\r\n                    if (statusData.can_resend) {\r\n                        $('#resend-section').removeClass('hidden');\r\n                    }\r\n                    $('#change-email-section').removeClass('hidden');\r\n                    $('#current-email').val(email);\r\n                    \r\n                    // Show attempt info if available\r\n                    if (statusData.verification_attempts) {\r\n                        statusHtml += `<p class=\"text-sm text-red-600 mt-2\">Verification attempts: ${statusData.verification_attempts}/3</p>`;\r\n                    }\r\n                    break;\r\n            }\r\n            \r\n            $statusDisplay.html(`<div class=\"border rounded-lg p-6 text-center ${statusClass}\">${statusHtml}</div>`);\r\n            $actionButtons.removeClass('hidden');\r\n        }\r\n        \r\n        // Handle resend verification\r\n        $(document).on('click', '#resend-verification-btn', function() {\r\n            const $btn = $(this);\r\n            $btn.prop('disabled', true).text('Sending...');\r\n            \r\n            $.ajax({\r\n                url: autohub_reg_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: {\r\n                    action: 'autohub_resend_verification',\r\n                    email: email,\r\n                    nonce: autohub_reg_ajax.nonce\r\n                },\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        showSuccess(response.data.message);\r\n                        $btn.text('Email Sent!');\r\n                        setTimeout(() => {\r\n                            $btn.prop('disabled', false).text('Resend Verification Email');\r\n                        }, 30000);\r\n                    } else {\r\n                        showError(response.data.message || 'Failed to send verification email');\r\n                        $btn.prop('disabled', false).text('Resend Verification Email');\r\n                    }\r\n                },\r\n                error: function() {\r\n                    showError('Connection error. Please try again.');\r\n                    $btn.prop('disabled', false).text('Resend Verification Email');\r\n                }\r\n            });\r\n        });\r\n        \r\n        // Handle email change\r\n        $(document).on('submit', '#change-email-form', function(e) {\r\n            e.preventDefault();\r\n            \r\n            const newEmail = $('#new-email').val();\r\n            const $btn = $('#change-email-btn');\r\n            \r\n            if (!newEmail || !isValidEmail(newEmail)) {\r\n                showError('Please enter a valid email address');\r\n                return;\r\n            }\r\n            \r\n            if (newEmail === email) {\r\n                showError('New email must be different from current email');\r\n                return;\r\n            }\r\n            \r\n            $btn.prop('disabled', true).text('Changing...');\r\n            \r\n            $.ajax({\r\n                url: autohub_reg_ajax.ajax_url,\r\n                type: 'POST',\r\n                data: {\r\n                    action: 'autohub_change_pending_email',\r\n                    old_email: email,\r\n                    new_email: newEmail,\r\n                    token: token || '',\r\n                    nonce: autohub_reg_ajax.nonce\r\n                },\r\n                success: function(response) {\r\n                    if (response.success) {\r\n                        showSuccess(response.data.message);\r\n                        // Update the URL and reload status\r\n                        const newUrl = new URL(window.location);\r\n                        newUrl.searchParams.set('email', newEmail);\r\n                        window.history.replaceState({}, '', newUrl);\r\n                        setTimeout(() => {\r\n                            window.location.reload();\r\n                        }, 2000);\r\n                    } else {\r\n                        showError(response.data.message || 'Failed to change email address');\r\n                        $btn.prop('disabled', false).text('Change Email Address');\r\n                    }\r\n                },\r\n                error: function() {\r\n                    showError('Connection error. Please try again.');\r\n                    $btn.prop('disabled', false).text('Change Email Address');\r\n                }\r\n            });\r\n        });\r\n        \r\n        function showSuccess(message) {\r\n            showMessage(message, 'success');\r\n        }\r\n        \r\n        function showError(message) {\r\n            showMessage(message, 'error');\r\n        }\r\n        \r\n        function showMessage(message, type) {\r\n            const $messagesDiv = $('#messages');\r\n            const alertClass = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';\r\n            \r\n            $messagesDiv.html(`\r\n                <div class=\"border rounded-lg p-4 ${alertClass}\">\r\n                    <p>${message}</p>\r\n                </div>\r\n            `);\r\n            \r\n            // Auto-hide after 5 seconds for success messages\r\n            if (type === 'success') {\r\n                setTimeout(() => {\r\n                    $messagesDiv.html('');\r\n                }, 5000);\r\n            }\r\n        }\r\n        \r\n        function isValidEmail(email) {\r\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n            return emailRegex.test(email);\r\n        }\r\n        \r\n        function showEmailInputForm() {\r\n            const $statusDisplay = $('#status-display');\r\n            const $actionButtons = $('#action-buttons');\r\n            \r\n            $statusDisplay.html(`\r\n                <div class=\"border rounded-lg p-6 text-center bg-blue-50 border-blue-200\">\r\n                    <h2 class=\"text-xl font-bold text-blue-700 mb-4\">Check Your Verification Status</h2>\r\n                    <p class=\"text-blue-600 mb-4\">Enter your email address to check your verification status:</p>\r\n                    <form id=\"email-check-form\" class=\"space-y-4\">\r\n                        <div>\r\n                            <input type=\"email\" id=\"email-check-input\" placeholder=\"Enter your email address\" required\r\n                                   class=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500\">\r\n                        </div>\r\n                        <button type=\"submit\" class=\"w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200\">\r\n                            Check Status\r\n                        </button>\r\n                    </form>\r\n                </div>\r\n            `);\r\n            \r\n            // Handle form submission\r\n            $(document).on('submit', '#email-check-form', function(e) {\r\n                e.preventDefault();\r\n                const enteredEmail = $('#email-check-input').val().trim();\r\n                \r\n                if (!enteredEmail || !isValidEmail(enteredEmail)) {\r\n                    showError('Please enter a valid email address');\r\n                    return;\r\n                }\r\n                \r\n                // Update URL and reload with email parameter\r\n                const newUrl = new URL(window.location);\r\n                newUrl.searchParams.set('email', enteredEmail);\r\n                window.location.href = newUrl.toString();\r\n            });\r\n            \r\n            $actionButtons.removeClass('hidden');\r\n        }\r\n    }\r\n\r\n})(jQuery);"]}