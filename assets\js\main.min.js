document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll("[data-location-province]").forEach(function(e){var t=e.getAttribute("data-location-province");let r=document.getElementById(t);r?e.addEventListener("change",function(){var e,t=this.value;r.innerHTML='<option value="">Select City/Town...</option>',r.disabled=!0,t&&(r.innerHTML='<option value="">Loading cities...</option>',(e=new FormData).append("action","get_cities_by_province"),e.append("province_id",t),fetch(autohub_ajax.ajax_url,{method:"POST",body:e}).then(e=>e.json()).then(e=>{e.success?(r.innerHTML='<option value="">Select City/Town...</option>',e.data.forEach(function(e){var t=document.createElement("option");t.value=e.id,t.textContent=e.title,r.appendChild(t)}),r.disabled=!1):(r.innerHTML='<option value="">Error loading cities</option>',console.error("Error loading cities:",e))}).catch(e=>{r.innerHTML='<option value="">Error loading cities</option>',console.error("AJAX error:",e)}))}):console.warn("City select element not found:",t)})}),"undefined"!=typeof Alpine&&Alpine.data("locationSelector",()=>({selectedProvince:"",selectedCity:"",cities:[],loadingCities:!1,async loadCities(){if(this.selectedProvince){this.loadingCities=!0,this.selectedCity="";try{var e=new FormData;e.append("action","get_cities_by_province"),e.append("province_id",this.selectedProvince);var t=await(await fetch(autohub_ajax.ajax_url,{method:"POST",body:e})).json();t.success?this.cities=t.data:(console.error("Error loading cities:",t),this.cities=[])}catch(e){console.error("AJAX error:",e),this.cities=[]}finally{this.loadingCities=!1}}else this.cities=[],this.selectedCity=""}})),(a=>{function e(){{let t=a(".mobile-menu-button"),r=a(".mobile-menu");t.on("click",function(e){e.preventDefault(),a(this).toggleClass("active"),r.toggleClass("active"),a("body").toggleClass("menu-open")}),a(document).on("click",function(e){a(e.target).closest(".mobile-menu, .mobile-menu-button").length||(t.removeClass("active"),r.removeClass("active"),a("body").removeClass("menu-open"))})}a('a[href*="#"]:not([href="#"])').on("click",function(e){var t=a(this.getAttribute("href"));t.length&&(e.preventDefault(),a("html, body").animate({scrollTop:t.offset().top-80},800))});{let e=a(".back-to-top");e.length&&(a(window).on("scroll",function(){300<a(this).scrollTop()?e.addClass("visible"):e.removeClass("visible")}),e.on("click",function(e){e.preventDefault(),a("html, body").animate({scrollTop:0},800)}))}a("form").on("submit",function(e){var t=a(this);let r=!0;t.find("[required]").each(function(){var e=a(this),t=e.val().trim();t?(e.removeClass("error"),n(e)):(r=!1,e.addClass("error"),s(e,"This field is required.")),"email"===e.attr("type")&&t&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)&&(r=!1,e.addClass("error"),s(e,"Please enter a valid email address."))}),r||e.preventDefault()}),a("input, textarea, select").on("input change",function(){var e=a(this);e.hasClass("error")&&(e.removeClass("error"),n(e))})}function s(e,t){n(e);t=a('<div class="field-error text-red-600 text-sm mt-1">'+t+"</div>");e.after(t)}function n(e){e.next(".field-error").remove()}function t(a,s,n){let i;return function(){let e=this,t=arguments;var r=n&&!i;clearTimeout(i),i=setTimeout(function(){i=null,n||a.apply(e,t)},s),r&&a.apply(e,t)}}a(document).ready(function(){e()}),a(window).on("resize",t(function(){},250)),window.AutoHubTheme={initializeTheme:e,debounce:t}})(jQuery),document.addEventListener("DOMContentLoaded",function(){let a=document.getElementById("add-vehicle-form");function s(e,t){let r=document.createElement("div");r.className="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full "+("success"===e?"bg-green-500 text-white":"bg-red-500 text-white"),r.innerHTML=`
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    ${"success"===e?'<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>':'<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>'}
                </svg>
                <span>${t}</span>
            </div>
        `,document.body.appendChild(r),setTimeout(()=>{r.classList.remove("translate-x-full")},100),setTimeout(()=>{r.classList.add("translate-x-full"),setTimeout(()=>{r.parentNode&&r.parentNode.removeChild(r)},300)},5e3)}a&&a.addEventListener("submit",function(e){e.preventDefault();let t=this.querySelector('button[type="submit"]'),r=t.textContent;t.disabled=!0,t.innerHTML='<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Adding Vehicle...';e=new FormData(this);e.append("action","add_vehicle"),e.append("nonce",document.querySelector("#add_vehicle_nonce").value),fetch(autohub_ajax.ajax_url,{method:"POST",body:e}).then(e=>e.json()).then(e=>{var t;e.success?(s("success",e.data.message),a.reset(),(t=document.querySelector("[x-data]"))&&t._x_dataStack&&(t._x_dataStack[0].showAddVehicleModal=!1),setTimeout(()=>{window.location.reload()},1500)):s("error",e.data||"An error occurred while adding the vehicle.")}).catch(e=>{console.error("Error:",e),s("error","An error occurred while adding the vehicle.")}).finally(()=>{t.disabled=!1,t.textContent=r})})}),(n=>{n(document).ready(function(){function s(e,t){let r=n('<div class="notice notice-'+t+' is-dismissible"><p>'+e+"</p></div>");n(".wrap h1").after(r),setTimeout(function(){r.fadeOut()},5e3)}n("#auto-approve-toggle").on("change",function(){let t=n(this).is(":checked");n.ajax({url:autohub_admin_ajax.ajax_url,type:"POST",data:{action:"autohub_toggle_auto_approve",auto_approve:t?"yes":"no",nonce:autohub_admin_ajax.nonce},success:function(e){e.success||(console.error("AutoHub Admin: Failed to update auto-approve setting"),n("#auto-approve-toggle").prop("checked",!t))},error:function(){console.error("AutoHub Admin: Error updating auto-approve setting"),n("#auto-approve-toggle").prop("checked",!t)}})}),n(".approve-user").on("click",function(){var e=n(this).data("user-id");let t=n(this),r=t.closest("tr");t.prop("disabled",!0).text("Approving..."),n.ajax({url:autohub_admin_ajax.ajax_url,type:"POST",data:{action:"autohub_approve_user",user_id:e,nonce:autohub_admin_ajax.nonce},success:function(e){e.success?(r.fadeOut(500,function(){n(this).remove()}),s("User approved successfully!","success")):(s(e.data.message||"Failed to approve user","error"),t.prop("disabled",!1).text("Approve"))},error:function(){s("Error approving user","error"),t.prop("disabled",!1).text("Approve")}})}),n(".reject-user").on("click",function(){var e=n(this).data("user-id");let t=n(this),r=t.closest("tr");confirm("Are you sure you want to reject this user? This action cannot be undone.")&&(t.prop("disabled",!0).text("Rejecting..."),n.ajax({url:autohub_admin_ajax.ajax_url,type:"POST",data:{action:"autohub_reject_user",user_id:e,nonce:autohub_admin_ajax.nonce},success:function(e){e.success?(r.fadeOut(500,function(){n(this).remove()}),s("User rejected successfully!","success")):(s(e.data.message||"Failed to reject user","error"),t.prop("disabled",!1).text("Reject"))},error:function(){s("Error rejecting user","error"),t.prop("disabled",!1).text("Reject")}}))}),n(".resend-verification").on("click",function(){n(this).data("user-id");var e=n(this).data("user-email");let t=n(this),r=t.closest("tr").find("td:nth-child(7)");t.prop("disabled",!0).text("Sending..."),n.ajax({url:autohub_admin_ajax.ajax_url,type:"POST",data:{action:"autohub_resend_verification",email:e,nonce:autohub_admin_ajax.nonce},success:function(e){e.success?(s(e.data.message,"success"),t.text("Email Sent!"),new Date,r.html('<span style="color: #dba617;">✉ Email Not Verified</span><br><small>Last sent: just now</small>'),setTimeout(function(){t.prop("disabled",!1).text("Resend Verification")},3e4)):(s(e.data.message||"Failed to send verification email","error"),t.prop("disabled",!1).text("Resend Verification"))},error:function(){s("Error sending verification email","error"),t.prop("disabled",!1).text("Resend Verification")}})}),n("#bulk-action-selector-top, #bulk-action-selector-bottom").on("change",function(){var e=n(this).val(),t=n(".user-checkbox:checked");if("resend-verification"===e&&0<t.length&&confirm("Send verification emails to "+t.length+" selected users?")){e=t;let r=0,a=e.length;e.each(function(){n(this).val();var e=n(this).data("user-email");let t=n(this).closest("tr");n.ajax({url:autohub_admin_ajax.ajax_url,type:"POST",data:{action:"autohub_resend_verification",email:e,nonce:autohub_admin_ajax.nonce},success:function(e){r++,e.success&&t.find(".resend-verification").text("Email Sent!"),r===a&&s("Bulk verification emails processed!","success")},error:function(){++r===a&&s("Bulk verification emails completed with some errors","warning")}})})}})})})(jQuery),(d=>{let n={username:/^[a-zA-Z0-9_-]+$/,email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^(\+260|0)[0-9]{9}$/,password:{minLength:8,hasLowercase:/[a-z]/,hasUppercase:/[A-Z]/,hasNumber:/[0-9]/,hasSpecial:/[!@#$%^&*(),.?":{}|<>]/}},i={username:{required:"Username is required",minLength:"Username must be at least 3 characters long",pattern:"Username can only contain letters, numbers, underscores, and hyphens",exists:"This username is already taken"},email:{required:"Email address is required",invalid:"Please enter a valid email address",exists:"This email address is already registered"},phone:{required:"Phone number is required",invalid:"Please enter a valid Zambian phone number (e.g., +260 XXX XXX XXX)"},password:{required:"Password is required",minLength:"Password must be at least 8 characters long",weak:"Password should contain uppercase, lowercase, number, and special character"},confirmPassword:{required:"Please confirm your password",mismatch:"Passwords do not match"},businessName:{required:"Business name is required",minLength:"Business name must be at least 2 characters long"},firstName:{required:"First name is required",minLength:"First name must be at least 2 characters long"},lastName:{required:"Last name is required",minLength:"Last name must be at least 2 characters long"},corporateName:{required:"Corporate name is required",minLength:"Corporate name must be at least 2 characters long"}};function l(t,r){let a;return function(...e){clearTimeout(a),a=setTimeout(()=>{clearTimeout(a),t(...e)},r)}}function c(e,t,r="error"){e=d(e);let a=e.siblings(".field-feedback");0===a.length&&(a=e.closest(".form-group").find(".field-feedback")),e.removeClass("border-red-500 border-green-500 border-gray-300"),a.removeClass("text-red-500 text-green-500 hidden"),"error"===r?(e.addClass("border-red-500"),a.addClass("text-red-500").text(t).removeClass("hidden")):"success"===r?(e.addClass("border-green-500"),a.addClass("text-green-500").text(t).removeClass("hidden")):(e.addClass("border-gray-300"),a.addClass("hidden"))}function u(e){var e=d(e),t=e.siblings(".field-feedback");e.removeClass("border-red-500 border-green-500").addClass("border-gray-300"),t.addClass("hidden")}function m(e,t){e?e.length<3?t(i.username.minLength,"error"):n.username.test(e)?d.ajax({url:autohub_reg_ajax.ajax_url,type:"POST",data:{action:"autohub_check_username",username:e,nonce:autohub_reg_ajax.nonce},success:function(e){e.success?e.data.available?t("Username is available","success"):t(i.username.exists,"error"):t(e.data.message||"Error checking username","error")},error:function(){t("Error checking username","error")}}):t(i.username.pattern,"error"):t(i.username.required,"error")}function h(e,t){e?n.email.test(e)?d.ajax({url:autohub_reg_ajax.ajax_url,type:"POST",data:{action:"autohub_check_email",email:e,nonce:autohub_reg_ajax.nonce},success:function(e){e.success?e.data.available?t("Email is available","success"):t(i.email.exists,"error"):t(e.data.message||"Error checking email","error")},error:function(){t("Error checking email","error")}}):t(i.email.invalid,"error"):t(i.email.required,"error")}function p(e){return e?(e=e.replace(/[\s-]/g,""),n.phone.test(e)?{valid:!0,message:"Valid phone number"}:{valid:!1,message:i.phone.invalid}):{valid:!1,message:i.phone.required}}function f(e){if(!e)return{valid:!1,message:i.password.required,strength:"none"};if(e.length<n.password.minLength)return{valid:!1,message:i.password.minLength,strength:"weak"};let t="weak",r=0;var a=[],e=(n.password.hasLowercase.test(e)?r++:a.push("lowercase letter"),n.password.hasUppercase.test(e)?r++:a.push("uppercase letter"),n.password.hasNumber.test(e)?r++:a.push("number"),n.password.hasSpecial.test(e)?r++:a.push("special character"),4<=r?t="strong":3<=r&&(t="medium"),3<=r);let s;return{valid:e,message:s=e?"Password strength: "+t:1===a.length?"Password needs: "+a[0]:2===a.length?"Password needs: "+a.join(" and "):`Password needs: ${a.slice(0,-1).join(", ")} and `+a[a.length-1],strength:t}}function g(e,t){return t?e!==t?{valid:!1,message:i.confirmPassword.mismatch}:{valid:!0,message:"Passwords match"}:{valid:!1,message:i.confirmPassword.required}}function b(e,t,r=2){return i[t]?e&&""!==e.trim()?e.trim().length<r?{valid:!1,message:i[t].minLength}:{valid:!0,message:""}:{valid:!1,message:i[t].required}:{valid:!1,message:"This field is required"}}function v(e){let t=!0,r=[];e.find("[required]").each(function(){var e=d(this);e.val().trim()||(e=e.closest(".form-group").find("label").text().replace("*","").trim(),r.push(e+" is required"),c(this,"This field is required","error"),t=!1)});var a=e.find('input[name="password"]'),s=(!a.length||(s=f(a.val())).valid||(r.push("Password does not meet requirements"),c(a[0],s.message,"error"),t=!1),e.find('input[name="confirm_password"]'));return s.length&&a.length&&((a=g(a.val(),s.val())).valid||(r.push("Password confirmation does not match"),c(s[0],a.message,"error"),t=!1)),e.find(".border-red-500").each(function(){t=!1}),{isValid:t,errors:r}}d(document).ready(function(){{let e=d("#autohub-registration-form");if((e=e.length?e:d("form").filter(function(){return 0<d(this).find('input[name="consumer_type"]').length})).length){e.find('input[name="consumer_type"]').on("change",function(){var e=d(this).val(),t=d("#individual-fields"),r=d("#corporate-fields");("corporate"===e?(t.addClass("hidden"),r.removeClass("hidden"),r.find("input").prop("disabled",!1).attr("required",!0),t.find("input").prop("disabled",!0).removeAttr("required"),t.find("input").removeClass("border-red-500 border-green-500").addClass("border-gray-300"),t):(t.removeClass("hidden"),r.addClass("hidden"),t.find("input").prop("disabled",!1).attr("required",!0),r.find("input").prop("disabled",!0).removeAttr("required"),r.find("input").removeClass("border-red-500 border-green-500").addClass("border-gray-300"),r)).find(".field-feedback").addClass("hidden")}),e.find('input[name="consumer_type"]:checked').trigger("change");let t=l(function(e,r){m(e,function(e,t){c(r,e,t)})},500),r=(e.find("#username").on("input blur",function(){var e=d(this).val().trim();e?t(e,this):u(this)}),l(function(e,r){h(e,function(e,t){c(r,e,t)})},500));e.find("#email").on("input blur",function(){var e=d(this).val().trim();e?r(e,this):u(this)}),e.find("#phone").on("input blur",function(){var e=d(this).val().trim(),t=p(e);e?c(this,t.message,t.valid?"success":"error"):u(this)})}}{let n=d("#autohub-business-owner-registration-form, #autohub-registration-form");if((n=n.length?n:d("form").filter(function(){return 0<d(this).find('input[name="password"], #consumer_password, #bo_password').length})).length){let t=l(function(e,r){m(e,function(e,t){c(r,e,t)})},500),r=(n.find("#consumer_username, #bo_username").on("input blur",function(){var e=d(this).val().trim();e?t(e,this):c(this,"Username is required","error")}),l(function(e,r){h(e,function(e,t){c(r,e,t)})},500));n.find("#email, #bo_email").on("input blur",function(){var e=d(this).val().trim();e?r(e,this):c(this,"Email address is required","error")}),n.find("#phone, #bo_phone").on("input blur",function(){var e=d(this).val().trim(),t=p(e);e?c(this,t.message,t.valid?"success":"error"):c(this,"Phone number is required","error")});var e=n.find('input[name="password"], #consumer_password, #bo_password');e.on("focus",function(){}),e.on("input keyup",function(){var e=d(this).val(),t=f(e),r=d(this).closest(".form-group").find(".password-strength-indicator"),a=r.find(".strength-text"),s=r.find(".strength-bar"),r=(r.removeClass("strength-weak strength-medium strength-strong"),"none"!==t.strength?(r.addClass("strength-"+t.strength),a.text(t.strength.charAt(0).toUpperCase()+t.strength.slice(1)),s.removeClass("bg-red-500 bg-orange-500 bg-green-500").addClass("bg-gray-200"),"weak"===t.strength?s.eq(0).removeClass("bg-gray-200").addClass("bg-red-500"):"medium"===t.strength?(s.eq(0).removeClass("bg-gray-200").addClass("bg-orange-500"),s.eq(1).removeClass("bg-gray-200").addClass("bg-orange-500")):"strong"===t.strength&&s.removeClass("bg-gray-200").addClass("bg-green-500")):(a.text(""),s.removeClass("bg-red-500 bg-orange-500 bg-green-500").addClass("bg-gray-200")),0<e.length?c(this,t.message,t.valid?"success":"error"):u(this),n.find('input[name="confirm_password"], #consumer_confirm_password, #bo_confirm_password').val());r&&(a=g(e,r),s=n.find('input[name="confirm_password"], #consumer_confirm_password, #bo_confirm_password')[0])&&c(s,a.message,a.valid?"success":"error")}),n.find('input[name="password"], #consumer_password, #bo_password').on("blur",function(){0===d(this).val().length&&c(this,"Password is required","error")}),n.find('input[name="confirm_password"], #consumer_confirm_password, #bo_confirm_password').on("input keyup",function(){var e=n.find('input[name="password"], #consumer_password, #bo_password').val(),t=d(this).val();0<t.length?c(this,(e=g(e,t)).message,e.valid?"success":"error"):u(this)}),n.find('input[name="confirm_password"], #consumer_confirm_password, #bo_confirm_password').on("blur",function(){0===d(this).val().length&&c(this,"Please confirm your password","error")}),n.find("#first_name, #last_name, #corporate_name, #contact_first_name, #contact_last_name, #bo_first_name, #bo_last_name").on("input blur",function(){var e=d(this).val().trim(),t=d(this).attr("id");let r="firstName";"last_name"===t||"contact_last_name"===t||"bo_last_name"===t?r="lastName":"corporate_name"===t&&(r="corporateName");var a=b(e,r);if(e)c(this,a.message,a.valid?"success":"error");else{let e="First name is required";"last_name"===t||"contact_last_name"===t||"bo_last_name"===t?e="Last name is required":"corporate_name"===t&&(e="Corporate name is required"),c(this,e,"error")}}),n.find("#business_name").on("input blur",function(){var e=d(this).val().trim(),t=b(e,"businessName");e?c(this,t.message,t.valid?"success":"error"):u(this)})}}d(document).on("click",".password-toggle",function(){var e=d(this),t=e.data("target"),t=d("#"+t),r=e.find(".eye-open"),e=e.find(".eye-closed");"password"===t.attr("type")?(t.attr("type","text"),r.addClass("hidden"),e.removeClass("hidden")):(t.attr("type","password"),r.removeClass("hidden"),e.addClass("hidden"))}),d(document).on("click","#resend-verification-btn",function(e){e.preventDefault();let a=d(this),s=d("#resend-verification-message");e=a.data("user-email");e?"undefined"==typeof autohub_reg_ajax?s.html('<div style="color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;">Error: AJAX not configured</div>').show():(a.prop("disabled",!0).text("Sending..."),s.hide(),d.ajax({url:autohub_reg_ajax.ajax_url,type:"POST",data:{action:"autohub_resend_verification",email:e,nonce:autohub_reg_ajax.nonce},success:function(e){e.success?(s.html('<div style="color: #2e7d32; padding: 10px; background: #e8f5e8; border-radius: 4px;">'+e.data.message+"</div>").show(),a.text("Email Sent!"),setTimeout(function(){a.prop("disabled",!1).text("Resend Verification Email")},3e4)):(s.html('<div style="color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;">'+(e.data.message||"Failed to send verification email")+"</div>").show(),a.prop("disabled",!1).text("Resend Verification Email"))},error:function(e,t,r){console.error("AutoHub: AJAX error:",r),console.error("AutoHub: XHR response:",e.responseText),s.html('<div style="color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;">Connection error. Please try again.</div>').show(),a.prop("disabled",!1).text("Resend Verification Email")}})):s.html('<div style="color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;">Error: No email address found</div>').show()});var e=d(".autohub-verification-status"),t=d("#status-display");if(d("#action-buttons"),e.length||t.length){e=new URLSearchParams(window.location.search);let i=e.get("email"),t=e.get("token");function s(e){r(e,"success")}function o(e){r(e,"error")}function r(e,t){let r=d("#messages");r.html(`
                <div class="border rounded-lg p-4 ${"success"===t?"bg-green-100 border-green-400 text-green-700":"bg-red-100 border-red-400 text-red-700"}">
                    <p>${e}</p>
                </div>
            `),"success"===t&&setTimeout(()=>{r.html("")},5e3)}function n(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}"invalid_link"===e.get("error")?o("Invalid or expired verification link. Please request a new verification email."):i?(d.ajax({url:autohub_reg_ajax.ajax_url,type:"POST",data:{action:"autohub_check_verification_status",email:i,token:t||""},success:function(s){if(s.success){var n=s.data;let e=d("#status-display"),t=d("#action-buttons"),r="",a="";switch(n.status){case"approved":a="bg-green-100 border-green-400 text-green-700",r=`
                        <div class="flex items-center justify-center mb-4">
                            <svg class="h-16 w-16 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-green-700 mb-2">Account Activated!</h2>
                        <p class="text-green-600">${n.message}</p>
                    `,d("#login-section").removeClass("hidden");break;case"pending_approval":a="bg-yellow-100 border-yellow-400 text-yellow-700",r=`
                        <div class="flex items-center justify-center mb-4">
                            <svg class="h-16 w-16 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-yellow-700 mb-2">Awaiting Approval</h2>
                        <p class="text-yellow-600">${n.message}</p>
                    `;break;case"verified":a="bg-blue-100 border-blue-400 text-blue-700",r=`
                        <div class="flex items-center justify-center mb-4">
                            <svg class="h-16 w-16 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-blue-700 mb-2">Email Verified</h2>
                        <p class="text-blue-600">${n.message}</p>
                    `,d("#login-section").removeClass("hidden");break;case"unverified":a="bg-red-100 border-red-400 text-red-700",r=`
                        <div class="flex items-center justify-center mb-4">
                            <svg class="h-16 w-16 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-red-700 mb-2">Email Not Verified</h2>
                        <p class="text-red-600">${n.message}</p>
                    `,n.can_resend&&d("#resend-section").removeClass("hidden"),d("#change-email-section").removeClass("hidden"),d("#current-email").val(i),n.verification_attempts&&(r+=`<p class="text-sm text-red-600 mt-2">Verification attempts: ${n.verification_attempts}/3</p>`)}e.html(`<div class="border rounded-lg p-6 text-center ${a}">${r}</div>`),t.removeClass("hidden")}else o(s.data.message||"Failed to check verification status")},error:function(){o("Connection error. Please try again.")}}),d(document).on("click","#resend-verification-btn",function(){let t=d(this);t.prop("disabled",!0).text("Sending..."),d.ajax({url:autohub_reg_ajax.ajax_url,type:"POST",data:{action:"autohub_resend_verification",email:i,nonce:autohub_reg_ajax.nonce},success:function(e){e.success?(s(e.data.message),t.text("Email Sent!"),setTimeout(()=>{t.prop("disabled",!1).text("Resend Verification Email")},3e4)):(o(e.data.message||"Failed to send verification email"),t.prop("disabled",!1).text("Resend Verification Email"))},error:function(){o("Connection error. Please try again."),t.prop("disabled",!1).text("Resend Verification Email")}})}),d(document).on("submit","#change-email-form",function(e){e.preventDefault();let r=d("#new-email").val(),a=d("#change-email-btn");r&&n(r)?r===i?o("New email must be different from current email"):(a.prop("disabled",!0).text("Changing..."),d.ajax({url:autohub_reg_ajax.ajax_url,type:"POST",data:{action:"autohub_change_pending_email",old_email:i,new_email:r,token:t||"",nonce:autohub_reg_ajax.nonce},success:function(e){var t;e.success?(s(e.data.message),(t=new URL(window.location)).searchParams.set("email",r),window.history.replaceState({},"",t),setTimeout(()=>{window.location.reload()},2e3)):(o(e.data.message||"Failed to change email address"),a.prop("disabled",!1).text("Change Email Address"))},error:function(){o("Connection error. Please try again."),a.prop("disabled",!1).text("Change Email Address")}})):o("Please enter a valid email address")})):(o("Missing email parameter. Please use the link from your verification email or enter your email below to check status."),(()=>{var e=d("#status-display"),t=d("#action-buttons");e.html(`
                <div class="border rounded-lg p-6 text-center bg-blue-50 border-blue-200">
                    <h2 class="text-xl font-bold text-blue-700 mb-4">Check Your Verification Status</h2>
                    <p class="text-blue-600 mb-4">Enter your email address to check your verification status:</p>
                    <form id="email-check-form" class="space-y-4">
                        <div>
                            <input type="email" id="email-check-input" placeholder="Enter your email address" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        <button type="submit" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                            Check Status
                        </button>
                    </form>
                </div>
            `),d(document).on("submit","#email-check-form",function(e){e.preventDefault();var t,e=d("#email-check-input").val().trim();e&&n(e)?((t=new URL(window.location)).searchParams.set("email",e),window.location.href=t.toString()):o("Please enter a valid email address")}),t.removeClass("hidden")})())}{let a=d("#plan-cost-display"),s=d('input[name="registration_type"]');if(0!==a.length&&0!==s.length)if("undefined"==typeof autohub_reg_ajax)console.warn("AutoHub Registration: AJAX object not available for plan costs");else{let r={};function i(e){var t=document.getElementById("plan-pricing-content"),e="auto_shop"===e?"auto_shop":"vehicle_professional";r[e]?t.innerHTML=r[e]:t.innerHTML='<div class="text-gray-600">Loading pricing...</div>',a.removeClass("hidden")}d.ajax({url:autohub_reg_ajax.ajax_url,type:"POST",data:{action:"autohub_get_plan_costs",nonce:autohub_reg_ajax.nonce},success:function(e){var t;e.success&&e.data?(r=e.data,(t=s.filter(":checked")).length&&i(t.val())):console.error("AutoHub Registration: Failed to load plan costs:",e)},error:function(e,t,r){console.error("AutoHub Registration: Error loading plan costs:",r)}}),s.on("change",function(){this.checked&&(i(this.value),d(".registration-type-option").removeClass("border-primary bg-primary-50").addClass("border-gray-300"),d(this).closest(".registration-type-option").removeClass("border-gray-300").addClass("border-primary bg-primary-50"))})}}d("form").filter(function(){var e=0<d(this).find('input[name="username"]').length,t=0<d(this).find('input[name="email"], input[name="business_email"]').length,r=0<d(this).find('input[name="password"]').length;return e&&t&&r});d("#autohub-registration-form").on("submit",function(e){e.preventDefault();let r=d(this),t=r.find('button[type="submit"]'),a=t.find(".btn-text"),s=t.find(".btn-loading"),n=d("#registration-messages");e=v(r);if(e.isValid){t.prop("disabled",!0),a.addClass("hidden"),s.removeClass("hidden"),n.addClass("hidden");var i=new FormData(r[0]);i.append("action","autohub_register_user"),d.ajax({url:autohub_reg_ajax.ajax_url,type:"POST",data:i,processData:!1,contentType:!1,success:function(e){e.success?(n.html('<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded"><div class="font-medium mb-2">Registration Successful!</div><p>'+e.data.message+"</p></div>"),n.removeClass("hidden"),r[0].reset(),r.find(".field-feedback").addClass("hidden"),r.find("input").removeClass("border-red-500 border-green-500").addClass("border-gray-300"),r.find(".password-strength-indicator").removeClass("strength-weak strength-medium strength-strong"),r.find(".strength-text").text(""),r.find(".strength-bar").removeClass("bg-red-500 bg-orange-500 bg-green-500").addClass("bg-gray-200"),r.find('input[name="consumer_type"][value="individual"]').prop("checked",!0),d("#individual-fields").removeClass("hidden"),d("#corporate-fields").addClass("hidden"),n[0].scrollIntoView({behavior:"smooth",block:"center"})):(n.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"><div class="font-medium mb-2">Registration Failed</div><p>'+(e.data.message||"An error occurred. Please try again.")+"</p></div>"),n.removeClass("hidden"))},error:function(e,t,r){console.error("Consumer registration error:",r);let a="Connection Error: ";500===e.status?a+="Server error occurred. Please try again or contact support.":0===e.status?a+="Unable to connect to the server. Please check your internet connection and try again.":a+="An unexpected error occurred. Please try again.",n.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"><div class="font-medium mb-2">Registration Failed</div><p>'+a+"</p></div>"),n.removeClass("hidden")},complete:function(){t.prop("disabled",!1),a.removeClass("hidden"),s.addClass("hidden")}})}else{let t='<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">';t=t+'<div class="font-medium mb-2">Please fix the following errors:</div>'+'<ul class="list-disc list-inside space-y-1">',e.errors.forEach(function(e){t+="<li>"+e+"</li>"}),t+="</ul></div>",n.html(t),n.removeClass("hidden");i=r.find(".border-red-500").first();void(i.length&&(i[0].scrollIntoView({behavior:"smooth",block:"center"}),i.focus()))}}),d("#autohub-business-owner-registration-form").on("submit",function(e){e.preventDefault();let r=d(this),t=r.find('button[type="submit"]'),a=t.find(".submit-text"),s=t.find(".loading-text"),n=d("#form-messages");e=v(r);if(e.isValid){t.prop("disabled",!0),a.addClass("hidden"),s.removeClass("hidden"),n.addClass("hidden");var i=new FormData(r[0]);i.append("action","autohub_register_business_owner"),d.ajax({url:autohub_reg_ajax.ajax_url,type:"POST",data:i,processData:!1,contentType:!1,success:function(e){e.success?(n.html('<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded"><div class="font-medium mb-2">Registration Successful!</div><p>'+e.data.message+"</p></div>"),n.removeClass("hidden"),r[0].reset(),r.find(".field-feedback").addClass("hidden"),r.find("input").removeClass("border-red-500 border-green-500").addClass("border-gray-300"),r.find(".password-strength-indicator").removeClass("strength-weak strength-medium strength-strong"),r.find(".strength-text").text(""),r.find(".strength-bar").removeClass("bg-red-500 bg-orange-500 bg-green-500").addClass("bg-gray-200"),n[0].scrollIntoView({behavior:"smooth",block:"center"})):(n.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"><div class="font-medium mb-2">Registration Failed</div><p>'+(e.data.message||"An error occurred. Please try again.")+"</p></div>"),n.removeClass("hidden"))},error:function(){n.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"><div class="font-medium mb-2">Connection Error</div><p>Unable to connect to the server. Please check your internet connection and try again.</p></div>'),n.removeClass("hidden")},complete:function(){t.prop("disabled",!1),a.removeClass("hidden"),s.addClass("hidden")}})}else{let t='<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">';t=t+'<div class="font-medium mb-2">Please fix the following errors:</div>'+'<ul class="list-disc list-inside space-y-1">',e.errors.forEach(function(e){t+="<li>"+e+"</li>"}),t+="</ul></div>",n.html(t),n.removeClass("hidden");i=r.find(".border-red-500").first();void(i.length&&(i[0].scrollIntoView({behavior:"smooth",block:"center"}),i.focus()))}});let a=d("#autohub-registration-form, #autohub-business-registration-form");(a=a.length?a:d("form").filter(function(){var e=0<d(this).find('input[name="username"]').length,t=(d(this).find('input[name="email"], input[name="business_email"]').length,0<d(this).find('input[name="password"]').length),r=d(this).attr("id");return e&&t&&"autohub-business-owner-registration-form"!==r})).on("submit",function(e){var r=v(d(this));if(!r.isValid){e.preventDefault();e=d(this).find('[id$="-messages"]');if(e.length){let t='<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">';t=t+'<div class="font-medium mb-2">Please fix the following errors:</div>'+'<ul class="list-disc list-inside space-y-1">',r.errors.forEach(function(e){t+="<li>"+e+"</li>"}),t+="</ul></div>",e.html(t),e.removeClass("hidden")}r=d(this).find(".border-red-500").first();r.length&&(r[0].scrollIntoView({behavior:"smooth",block:"center"}),r.focus())}})})})(jQuery);
//# sourceMappingURL=main.min.js.map
