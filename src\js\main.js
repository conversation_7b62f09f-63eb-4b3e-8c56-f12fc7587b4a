/**
 * AutoHub Zambia Theme JavaScript
 * Main JavaScript file for theme functionality
 */

(function($) {
    'use strict';

    // DOM Ready
    $(document).ready(function() {
        initializeTheme();
    });

    // Initialize theme functionality
    function initializeTheme() {
        initMobileMenu();
        initSmoothScroll();
        initBackToTop();
        initFormValidation();
    }

    // Mobile menu toggle
    function initMobileMenu() {
        const mobileMenuButton = $('.mobile-menu-button');
        const mobileMenu = $('.mobile-menu');

        mobileMenuButton.on('click', function(e) {
            e.preventDefault();
            $(this).toggleClass('active');
            mobileMenu.toggleClass('active');
            $('body').toggleClass('menu-open');
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.mobile-menu, .mobile-menu-button').length) {
                mobileMenuButton.removeClass('active');
                mobileMenu.removeClass('active');
                $('body').removeClass('menu-open');
            }
        });
    }

    // Smooth scroll for anchor links
    function initSmoothScroll() {
        $('a[href*="#"]:not([href="#"])').on('click', function(e) {
            const target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 800);
            }
        });
    }

    // Back to top button
    function initBackToTop() {
        const backToTop = $('.back-to-top');
        
        if (backToTop.length) {
            $(window).on('scroll', function() {
                if ($(this).scrollTop() > 300) {
                    backToTop.addClass('visible');
                } else {
                    backToTop.removeClass('visible');
                }
            });

            backToTop.on('click', function(e) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: 0
                }, 800);
            });
        }
    }

    // Basic form validation
    function initFormValidation() {
        $('form').on('submit', function(e) {
            const form = $(this);
            let isValid = true;

            // Check required fields
            form.find('[required]').each(function() {
                const field = $(this);
                const value = field.val().trim();

                if (!value) {
                    isValid = false;
                    field.addClass('error');
                    showFieldError(field, 'This field is required.');
                } else {
                    field.removeClass('error');
                    hideFieldError(field);
                }

                // Email validation
                if (field.attr('type') === 'email' && value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        field.addClass('error');
                        showFieldError(field, 'Please enter a valid email address.');
                    }
                }
            });

            if (!isValid) {
                e.preventDefault();
            }
        });

        // Remove error styling on input
        $('input, textarea, select').on('input change', function() {
            const field = $(this);
            if (field.hasClass('error')) {
                field.removeClass('error');
                hideFieldError(field);
            }
        });
    }

    // Show field error message
    function showFieldError(field, message) {
        hideFieldError(field);
        const errorDiv = $('<div class="field-error text-red-600 text-sm mt-1">' + message + '</div>');
        field.after(errorDiv);
    }

    // Hide field error message
    function hideFieldError(field) {
        field.next('.field-error').remove();
    }

    // Utility function for debouncing
    function debounce(func, wait, immediate) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // Window resize handler with debounce
    $(window).on('resize', debounce(function() {
        // Handle responsive adjustments here
    }, 250));

    // Expose functions globally if needed
    window.AutoHubTheme = {
        initializeTheme: initializeTheme,
        debounce: debounce
    };

})(jQuery);

/**
 * Vehicle Management JavaScript
 * Handles vehicle form submissions and related functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Handle Add Vehicle Form Submission
    const addVehicleForm = document.getElementById('add-vehicle-form');
    
    if (addVehicleForm) {
        addVehicleForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            
            // Show loading state
            submitButton.disabled = true;
            submitButton.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Adding Vehicle...';
            
            // Prepare form data
            const formData = new FormData(this);
            formData.append('action', 'add_vehicle');
            formData.append('nonce', document.querySelector('#add_vehicle_nonce').value);
            
            // Send AJAX request
            fetch(autohub_ajax.ajax_url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showNotification('success', data.data.message);
                    
                    // Reset form
                    addVehicleForm.reset();
                    
                    // Close modal (using Alpine.js)
                    const modalElement = document.querySelector('[x-data]');
                    if (modalElement && modalElement._x_dataStack) {
                        modalElement._x_dataStack[0].showAddVehicleModal = false;
                    }
                    
                    // Refresh the page to show the new vehicle
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                    
                } else {
                    showNotification('error', data.data || 'An error occurred while adding the vehicle.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('error', 'An error occurred while adding the vehicle.');
            })
            .finally(() => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            });
        });
    }
    
    // Notification function
    function showNotification(type, message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    ${type === 'success' 
                        ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>'
                        : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>'
                    }
                </svg>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }
});