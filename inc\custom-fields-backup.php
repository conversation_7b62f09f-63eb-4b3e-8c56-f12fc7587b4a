<?php
/**
 * Custom Fields Registration using Secure Custom Fields (SCF)
 * 
 * Registers all custom fields for the AutoHub Zambia theme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Register custom fields using SCF (which uses ACF syntax)
function autohub_register_listing_fields() {
    
    // Ensure SCF/ACF is fully loaded and ready
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }
    
    // Ensure post types are registered before field registration
    if (!post_type_exists('listing') || !post_type_exists('service_center') ||
        !post_type_exists('my_vehicle') || !post_type_exists('request') ||
        !post_type_exists('review') || !post_type_exists('province') ||
        !post_type_exists('city_town') || !post_type_exists('vehicle_professional')) {
        // Post types not ready yet, exit silently
        return;
    }
    
    // Add error handling for field registration
    try {
    
    // Listing Details Group
    acf_add_local_field_group(array(
        'key' => 'group_listing_details',
        'title' => 'Listing Details',
        'fields' => array(
            array(
                'key' => 'field_linked_service_center',
                'label' => 'Service Center',
                'name' => 'linked_service_center',
                'type' => 'relationship',
                'post_type' => array('service_center'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_brand_name',
                'label' => 'Brand Name',
                'name' => 'brand_name',
                'type' => 'text',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_condition',
                'label' => 'Condition',
                'name' => 'condition',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'New' => 'New',
                    'Used' => 'Used',
                    'Refurbished' => 'Refurbished',
                ),
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_part_number',
                'label' => 'Part Number',
                'name' => 'part_number',
                'type' => 'text',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_stock_status',
                'label' => 'Stock Status',
                'name' => 'stock_status',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'In Stock' => 'In Stock',
                    'Out of Stock' => 'Out of Stock',
                    'Expected' => 'Expected',
                ),
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_price',
                'label' => 'Price (ZMW)',
                'name' => 'price',
                'type' => 'number',
                'required' => 1,
                'min' => 0,
                'step' => 0.01,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_compatible_vehicles',
                'label' => 'Compatible Vehicles',
                'name' => 'compatible_vehicles',
                'type' => 'text',
                'placeholder' => 'e.g., Toyota Corolla 2005-2010',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_fuel_type',
                'label' => 'Fuel Type',
                'name' => 'fuel_type',
                'type' => 'select',
                'choices' => array(
                    'All' => 'All',
                    'Petrol' => 'Petrol',
                    'Diesel' => 'Diesel',
                ),
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_listing_images',
                'label' => 'Listing Images',
                'name' => 'listing_images',
                'type' => 'gallery',
                'return_format' => 'url',
                'preview_size' => 'medium',
                'library' => 'all',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'listing',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

    // Service Center Details Group
    acf_add_local_field_group(array(
        'key' => 'group_service_center_details',
        'title' => 'Service Center Details',
        'fields' => array(
            array(
                'key' => 'field_service_center_name',
                'label' => 'Service Center Name',
                'name' => 'service_center_name',
                'type' => 'text',
                'required' => 1,
                'maxlength' => 50,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_service_center_type',
                'label' => 'Service Center Type',
                'name' => 'service_center_type',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'garage' => 'Garage',
                    'body_shop' => 'Body Shop',
                    'dealership' => 'Dealership',
                ),
                'wrapper' => array(
                    'width' => '50',
                ),
            ),

            array(
                'key' => 'field_header_image',
                'label' => 'Header Image',
                'name' => 'header_image',
                'type' => 'image',
                'return_format' => 'url',
                'preview_size' => 'medium',
                'library' => 'all',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_phone',
                'label' => 'Phone',
                'name' => 'phone',
                'type' => 'text',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_email',
                'label' => 'Email',
                'name' => 'email',
                'type' => 'email',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_website',
                'label' => 'Website',
                'name' => 'website',
                'type' => 'url',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_address',
                'label' => 'Address',
                'name' => 'address',
                'type' => 'text',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_province',
                'label' => 'Province',
                'name' => 'province',
                'type' => 'relationship',
                'post_type' => array('province'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_city',
                'label' => 'City',
                'name' => 'city',
                'type' => 'relationship',
                'post_type' => array('city_town'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_services_offered',
                'label' => 'Services Offered',
                'name' => 'services_offered',
                'type' => 'repeater',
                'sub_fields' => array(
                    array(
                        'key' => 'field_service_name',
                        'label' => 'Service Name',
                        'name' => 'service_name',
                        'type' => 'text',
                        'required' => 1,
                    ),
                    array(
                        'key' => 'field_service_description',
                        'label' => 'Service Description',
                        'name' => 'service_description',
                        'type' => 'textarea',
                        'rows' => 2,
                    ),
                ),
                'min' => 1,
                'layout' => 'table',
                'button_label' => 'Add Service',
            ),
            array(
                'key' => 'field_owner_user',
                'label' => 'Business Owner',
                'name' => 'owner_user',
                'type' => 'user',
                'role' => array('business_owner', 'administrator'),
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),

        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'service_center',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

    // My Vehicle Details Group
    acf_add_local_field_group(array(
        'key' => 'group_my_vehicle_details',
        'title' => 'Vehicle Details',
        'fields' => array(
            array(
                'key' => 'field_vehicle_make',
                'label' => 'Vehicle Make',
                'name' => 'vehicle_make',
                'type' => 'text',
                'required' => 1,
                'placeholder' => 'e.g., Toyota',
                'wrapper' => array(
                    'width' => '33.33',
                ),
            ),
            array(
                'key' => 'field_vehicle_model',
                'label' => 'Vehicle Model',
                'name' => 'vehicle_model',
                'type' => 'text',
                'required' => 1,
                'placeholder' => 'e.g., Corolla',
                'wrapper' => array(
                    'width' => '33.33',
                ),
            ),
            array(
                'key' => 'field_vehicle_year',
                'label' => 'Vehicle Year',
                'name' => 'vehicle_year',
                'type' => 'number',
                'required' => 1,
                'min' => 1980,
                'max' => date('Y') + 1,
                'wrapper' => array(
                    'width' => '33.33',
                ),
            ),
            array(
                'key' => 'field_vehicle_fuel_type',
                'label' => 'Fuel Type',
                'name' => 'fuel_type',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'All' => 'All',
                    'Petrol' => 'Petrol',
                    'Diesel' => 'Diesel',
                ),
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_vin_number',
                'label' => 'VIN Number',
                'name' => 'vin_number',
                'type' => 'text',
                'placeholder' => 'Optional',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_vehicle_images',
                'label' => 'Vehicle Images',
                'name' => 'vehicle_images',
                'type' => 'gallery',
                'return_format' => 'url',
                'preview_size' => 'medium',
                'library' => 'all',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'my_vehicle',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

    // Request Details Group
    acf_add_local_field_group(array(
        'key' => 'group_request_details',
        'title' => 'Request Details',
        'fields' => array(
            array(
                'key' => 'field_linked_vehicle',
                'label' => 'My Vehicle',
                'name' => 'linked_vehicle',
                'type' => 'relationship',
                'post_type' => array('my_vehicle'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
            ),
            array(
                'key' => 'field_part_name',
                'label' => 'Part Name',
                'name' => 'part_name',
                'type' => 'text',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_preferred_condition',
                'label' => 'Preferred Condition',
                'name' => 'preferred_condition',
                'type' => 'select',
                'choices' => array(
                    'New' => 'New',
                    'Used' => 'Used',
                    'Refurbished' => 'Refurbished',
                    'Any' => 'Any',
                ),
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_part_description',
                'label' => 'Part Description',
                'name' => 'part_description',
                'type' => 'textarea',
                'rows' => 3,
            ),
            array(
                'key' => 'field_preferred_brand',
                'label' => 'Preferred Brand',
                'name' => 'preferred_brand',
                'type' => 'text',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_budget',
                'label' => 'Budget (ZMW)',
                'name' => 'budget',
                'type' => 'number',
                'min' => 0,
                'step' => 0.01,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_request_status',
                'label' => 'Request Status',
                'name' => 'request_status',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'Open' => 'Open',
                    'Closed' => 'Closed',
                    'Fulfilled' => 'Fulfilled',
                ),
                'default_value' => 'Open',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'request',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

    // Review Details Group
    acf_add_local_field_group(array(
        'key' => 'group_review_details',
        'title' => 'Review Details',
        'fields' => array(
            array(
                'key' => 'field_review_type',
                'label' => 'Review Type',
                'name' => 'review_type',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    'service_center' => 'Service Center',
                    'listing' => 'Listing',
                ),
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_target_post_id',
                'label' => 'Target Post',
                'name' => 'target_post_id',
                'type' => 'relationship',
                'post_type' => array('service_center', 'listing'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_rating',
                'label' => 'Rating',
                'name' => 'rating',
                'type' => 'select',
                'required' => 1,
                'choices' => array(
                    '1' => '1 Star',
                    '2' => '2 Stars',
                    '3' => '3 Stars',
                    '4' => '4 Stars',
                    '5' => '5 Stars',
                ),
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_review_text',
                'label' => 'Review Text',
                'name' => 'review_text',
                'type' => 'textarea',
                'required' => 1,
                'rows' => 4,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'review',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

    // Province Information Group
    acf_add_local_field_group(array(
        'key' => 'group_province_metadata',
        'title' => 'Province Information',
        'fields' => array(
            array(
                'key' => 'field_province_name',
                'label' => 'Province Name',
                'name' => 'province_name',
                'type' => 'text',
                'required' => 1,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'province',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

    // City/Town Information Group
    acf_add_local_field_group(array(
        'key' => 'group_city_town_metadata',
        'title' => 'City/Town Information',
        'fields' => array(
            array(
                'key' => 'field_city_town_name',
                'label' => 'City/Town Name',
                'name' => 'city_town_name',
                'type' => 'text',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_related_province',
                'label' => 'Province',
                'name' => 'related_province',
                'type' => 'relationship',
                'post_type' => array('province'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'city_town',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

    // Mechanic Details Group
    acf_add_local_field_group(array(
        'key' => 'group_mechanic_details',
        'title' => 'Mechanic Details',
        'fields' => array(
            array(
                'key' => 'field_mechanic_name',
                'label' => 'Mechanic Name',
                'name' => 'mechanic_name',
                'type' => 'text',
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_specialization',
                'label' => 'Specialization',
                'name' => 'specialization',
                'type' => 'repeater',
                'sub_fields' => array(
                    array(
                        'key' => 'field_specialization_item',
                        'label' => 'Specialization',
                        'name' => 'specialization_item',
                        'type' => 'text',
                        'required' => 1,
                        'placeholder' => 'e.g., Engine Repair, Body Work',
                    ),
                ),
                'min' => 1,
                'layout' => 'table',
                'button_label' => 'Add Specialization',
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_experience_years',
                'label' => 'Years of Experience',
                'name' => 'experience_years',
                'type' => 'number',
                'min' => 0,
                'wrapper' => array(
                    'width' => '33.33',
                ),
            ),
            array(
                'key' => 'field_phone_number',
                'label' => 'Phone Number',
                'name' => 'phone_number',
                'type' => 'text',
                'required' => 1,
                'wrapper' => array(
                    'width' => '33.33',
                ),
            ),
            array(
                'key' => 'field_mechanic_email',
                'label' => 'Email Address',
                'name' => 'email_address',
                'type' => 'email',
                'wrapper' => array(
                    'width' => '33.33',
                ),
            ),
            array(
                'key' => 'field_location',
                'label' => 'Location',
                'name' => 'location',
                'type' => 'text',
                'required' => 1,
                'wrapper' => array(
                    'width' => '100',
                ),
            ),
            array(
                'key' => 'field_mechanic_province',
                'label' => 'Province',
                'name' => 'province',
                'type' => 'relationship',
                'post_type' => array('province'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_mechanic_city',
                'label' => 'City/Town',
                'name' => 'city',
                'type' => 'relationship',
                'post_type' => array('city_town'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'mechanic',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));

    // Body Specialist Details Group
    acf_add_local_field_group(array(
        'key' => 'group_body_specialist_details',
        'title' => 'Body Specialist Details',
        'fields' => array(
            array(
                'key' => 'field_body_specialist_full_name',
                'label' => 'Full Name',
                'name' => 'full_name',
                'type' => 'text',
                'required' => 1,
                'maxlength' => 100,
                'instructions' => 'Professional\'s full name for display and SEO',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_profile_photo',
                'label' => 'Profile Photo',
                'name' => 'profile_photo',
                'type' => 'image',
                'return_format' => 'id',
                'preview_size' => 'medium',
                'library' => 'all',
                'instructions' => 'Headshot or professional photo',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_short_bio',
                'label' => 'Short Bio',
                'name' => 'short_bio',
                'type' => 'textarea',
                'maxlength' => 300,
                'rows' => 3,
                'instructions' => 'Brief intro or summary (max 300 characters)',
                'wrapper' => array(
                    'width' => '100',
                ),
            ),

            array(
                'key' => 'field_body_specialist_specializations',
                'label' => 'Specializations',
                'name' => 'specializations',
                'type' => 'repeater',
                'instructions' => 'Areas of expertise',
                'sub_fields' => array(
                    array(
                        'key' => 'field_specialization_item',
                        'label' => 'Specialization',
                        'name' => 'specialization_item',
                        'type' => 'select',
                        'required' => 1,
                        'choices' => array(
                            'Panel Beating' => 'Panel Beating',
                            'Painting' => 'Painting',
                            'Dent Removal' => 'Dent Removal',
                            'Window Repair' => 'Window Repair',
                            'Rust Repair' => 'Rust Repair',
                            'Collision Repair' => 'Collision Repair',
                            'Custom Paint Work' => 'Custom Paint Work',
                            'Scratch Repair' => 'Scratch Repair',
                        ),
                    ),
                ),
                'min' => 1,
                'layout' => 'table',
                'button_label' => 'Add Specialization',
            ),

            array(
                'key' => 'field_body_specialist_phone_1',
                'label' => 'Primary Phone',
                'name' => 'phone_1',
                'type' => 'text',
                'required' => 1,
                'instructions' => 'Primary contact number',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_phone_2',
                'label' => 'Secondary Phone',
                'name' => 'phone_2',
                'type' => 'text',
                'instructions' => 'Secondary contact number (optional)',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_email',
                'label' => 'Email',
                'name' => 'email',
                'type' => 'email',
                'required' => 1,
                'instructions' => 'Contact email address',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_physical_address',
                'label' => 'Physical Address',
                'name' => 'physical_address',
                'type' => 'textarea',
                'rows' => 2,
                'instructions' => 'Location/address or general area of operation',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_province',
                'label' => 'Province',
                'name' => 'province',
                'type' => 'relationship',
                'post_type' => array('province'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
                'instructions' => 'Province or region served',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_city_town',
                'label' => 'City/Town',
                'name' => 'city_town',
                'type' => 'relationship',
                'post_type' => array('city_town'),
                'filters' => array('search'),
                'max' => 1,
                'required' => 1,
                'instructions' => 'City or town served',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_service_rate',
                'label' => 'Service Rate',
                'name' => 'service_rate',
                'type' => 'number',
                'min' => 0,
                'step' => 0.01,
                'instructions' => 'Hourly rate or job-based fee (optional)',
                'append' => 'ZMW/hour',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_portfolio_images',
                'label' => 'Portfolio Images',
                'name' => 'portfolio_images',
                'type' => 'repeater',
                'instructions' => 'Before/after work examples or portfolio photos',
                'sub_fields' => array(
                    array(
                        'key' => 'field_portfolio_image',
                        'label' => 'Portfolio Image',
                        'name' => 'portfolio_image',
                        'type' => 'image',
                        'return_format' => 'id',
                        'preview_size' => 'medium',
                        'library' => 'all',
                    ),
                    array(
                        'key' => 'field_portfolio_description',
                        'label' => 'Description',
                        'name' => 'portfolio_description',
                        'type' => 'text',
                        'instructions' => 'Brief description of the work (optional)',
                    ),
                ),
                'min' => 0,
                'layout' => 'table',
                'button_label' => 'Add Portfolio Image',
            ),
            array(
                'key' => 'field_body_specialist_social_media_links',
                'label' => 'Social Media Links',
                'name' => 'social_media_links',
                'type' => 'repeater',
                'instructions' => 'Links to social profiles',
                'sub_fields' => array(
                    array(
                        'key' => 'field_social_platform',
                        'label' => 'Platform',
                        'name' => 'platform',
                        'type' => 'select',
                        'required' => 1,
                        'choices' => array(
                            'facebook' => 'Facebook',
                            'instagram' => 'Instagram',
                            'linkedin' => 'LinkedIn',
                            'twitter' => 'Twitter',
                            'youtube' => 'YouTube',
                            'tiktok' => 'TikTok',
                        ),
                        'wrapper' => array(
                            'width' => '30',
                        ),
                    ),
                    array(
                        'key' => 'field_social_url',
                        'label' => 'URL',
                        'name' => 'url',
                        'type' => 'url',
                        'required' => 1,
                        'wrapper' => array(
                            'width' => '70',
                        ),
                    ),
                ),
                'min' => 0,
                'layout' => 'table',
                'button_label' => 'Add Social Media Link',
            ),
            array(
                'key' => 'field_body_specialist_website_url',
                'label' => 'Website URL',
                'name' => 'website_url',
                'type' => 'url',
                'instructions' => 'Personal or professional website or portfolio',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
            array(
                'key' => 'field_body_specialist_certifications',
                'label' => 'Certifications & Qualifications',
                'name' => 'certifications',
                'type' => 'textarea',
                'rows' => 4,
                'instructions' => 'Professional qualifications or certificates',
                'wrapper' => array(
                    'width' => '50',
                ),
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'body_specialist',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
    ));
    
    } catch (Exception $e) {
        // Log error but don't break the site
        error_log('Custom Fields Registration Error: ' . $e->getMessage());
    }
}

// Hook into init with high priority to ensure post types are registered first
add_action('init', 'autohub_register_listing_fields', 20);

// Also hook into acf/init as a fallback
add_action('acf/init', 'autohub_register_listing_fields');