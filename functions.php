<?php
/**
 * AutoHub Zambia Theme functions and definitions
 *
 * @package AutoHub_Zambia
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Start session for 2FA functionality
if (!session_id()) {
    session_start();
}

// Theme version
define('AUTOHUB_VERSION', '1.0.0');

/**
 * Safe SCF field getter with fallback to get_post_meta
 * 
 * @param string $field_name The field name to retrieve
 * @param int|null $post_id The post ID (optional, defaults to current post)
 * @param bool $single Whether to return a single value (default: true)
 * @return mixed The field value or false if not found
 */
function autohub_get_field($field_name, $post_id = null, $single = true) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    // Try SCF first if available
    if (class_exists('SCF')) {
        return SCF::get($field_name, $post_id);
    }
    
    // Fallback to get_post_meta
    return get_post_meta($post_id, $field_name, $single);
}

/**
 * Format plan pricing with discount display
 * 
 * @param int $plan_id The listing plan post ID
 * @param string $css_class Additional CSS classes for the container
 * @return string Formatted HTML for plan pricing
 */
function autohub_get_plan_pricing($plan_id, $css_class = '') {
    $plan_cost = get_post_meta($plan_id, 'plan_cost', true);
    $discounted_cost = get_post_meta($plan_id, 'plan_cost_discounted', true);
    $discount_message = get_post_meta($plan_id, 'discount_message', true);
    $payment_frequency = get_post_meta($plan_id, 'payment_frequency', true);
    
    if (empty($plan_cost)) {
        return '<div class="' . esc_attr($css_class) . '">Contact for pricing</div>';
    }
    
    $frequency = ucfirst($payment_frequency);
    $container_class = 'plan-pricing ' . esc_attr($css_class);
    
    // Check if there's a valid discount
    if (!empty($discounted_cost) && $discounted_cost > 0 && $discounted_cost < $plan_cost) {
        // Calculate discount percentage
        $discount_percent = round((($plan_cost - $discounted_cost) / $plan_cost) * 100);
        $savings_amount = $plan_cost - $discounted_cost;
        
        ob_start();
        ?>
        <div class="<?php echo esc_attr($container_class); ?>">
            <?php if (!empty($discount_message)): ?>
                <div class="discount-message text-xs text-orange-600 font-medium mb-1">
                    <?php echo esc_html($discount_message); ?>
                </div>
            <?php endif; ?>
            
            <div class="original-price text-sm text-gray-500 line-through" style="text-decoration: line-through;">
                ZMW <?php echo number_format($plan_cost, 2); ?> <?php echo esc_html($frequency); ?>
            </div>
            
            <div class="discounted-price text-lg font-bold text-green-600">
                ZMW <?php echo number_format($discounted_cost, 2); ?> <?php echo esc_html($frequency); ?>
            </div>
            
            <div class="savings text-xs text-green-600 font-medium">
                Save <?php echo $discount_percent; ?>% (ZMW <?php echo number_format($savings_amount, 2); ?>)
            </div>
        </div>
        <?php
        return ob_get_clean();
    } else {
        // No discount, show regular price
        return '<div class="' . esc_attr($container_class) . '">
            <div class="regular-price text-lg font-semibold text-gray-900">
                ZMW ' . number_format($plan_cost, 2) . ' ' . esc_html($frequency) . '
            </div>
        </div>';
    }
}

/**
 * Display plan pricing (echo version)
 * 
 * @param int $plan_id The listing plan post ID
 * @param string $css_class Additional CSS classes for the container
 */
function autohub_plan_pricing($plan_id, $css_class = '') {
    echo autohub_get_plan_pricing($plan_id, $css_class);
}

// Theme setup
function autohub_setup() {
    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Add custom image sizes
    add_image_size('autohub-featured', 800, 400, true);
    add_image_size('autohub-thumbnail', 300, 200, true);
    add_image_size('autohub-large', 1200, 600, true);
    add_image_size('autohub-page-header', 1280, 500, true); // Page header size matching max content width
    add_image_size('autohub-logo', 200, 200, true); // Square logo size for uniform thumbnails

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'autohubzambia'),
        'footer'  => esc_html__('Footer Menu', 'autohubzambia'),
    ));

    // Switch default core markup to output valid HTML5
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));

    // Add theme support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');

    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 100,
        'width'       => 300,
        'flex-width'  => true,
        'flex-height' => true,
    ));

    // Add support for custom background
    add_theme_support('custom-background', array(
        'default-color' => 'ffffff',
    ));

    // Add support for editor styles
    add_theme_support('editor-styles');
    add_editor_style('assets/css/editor-style.css');

    // Add support for responsive embeds
    add_theme_support('responsive-embeds');

    // Add support for block styles
    add_theme_support('wp-block-styles');

    // Add support for wide alignment
    add_theme_support('align-wide');
}
add_action('after_setup_theme', 'autohub_setup');

// Set content width
function autohub_content_width() {
    $GLOBALS['content_width'] = apply_filters('autohub_content_width', 1200);
}
add_action('after_setup_theme', 'autohub_content_width', 0);

// Enqueue scripts and styles
function autohub_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style(
        'autohub-style',
        get_template_directory_uri() . '/assets/css/main.min.css',
        array(),
        AUTOHUB_VERSION
    );
    
    // Enqueue dashboard styles for logged-in users with consumer role
    if (is_user_logged_in() && in_array('consumer', wp_get_current_user()->roles)) {
        wp_enqueue_style(
            'autohub-dashboard',
            get_template_directory_uri() . '/assets/css/dashboard.css',
            array('autohub-style'),
            AUTOHUB_VERSION
        );
    }

    // Enqueue HTMX
    wp_enqueue_script(
        'htmx',
        get_template_directory_uri() . '/assets/js/htmx.min.js',
        array(),
        '1.9.10',
        true
    );

    // Enqueue Alpine.js
    wp_enqueue_script(
        'alpinejs',
        get_template_directory_uri() . '/assets/js/cdn.min.js',
        array(),
        '3.13.5',
        true
    );
    
    // Add Alpine.js configuration to prevent double initialization
    wp_add_inline_script('alpinejs', '
        // Configure Alpine.js to prevent double initialization
        document.addEventListener("DOMContentLoaded", function() {
            if (typeof Alpine !== "undefined" && !Alpine.version) {
                console.warn("Alpine.js not loaded properly");
            }
        });
    ', 'after');

    // Enqueue main JavaScript (load in footer to prevent FOUC)
    wp_enqueue_script(
        'autohub-script',
        get_template_directory_uri() . '/assets/js/main.min.js',
        array('jquery', 'htmx', 'alpinejs'),
        AUTOHUB_VERSION,
        true // Load in footer
    );

    // Enqueue Google Fonts
    wp_enqueue_style(
        'autohub-google-fonts',
        'https://fonts.googleapis.com/css2?family=Noto+Sans:wght@300;400;500;600;700&family=Noto+Serif:wght@400;500;600;700&display=swap',
        array(),
        null
    );

    // Enqueue comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }

    // Localize script for AJAX
    wp_localize_script('autohub-script', 'autohub_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('autohub_nonce'),
    ));
    
    // Also add registration-specific AJAX localization
    wp_localize_script('autohub-script', 'autohub_reg_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('autohub_reg_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'autohub_scripts');

// Register widget areas
function autohub_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Sidebar', 'autohubzambia'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here.', 'autohubzambia'),
        'before_widget' => '<section id="%1$s" class="widget %2$s mb-8">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title text-xl font-serif font-semibold mb-4">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => esc_html__('Footer Widget Area 1', 'autohubzambia'),
        'id'            => 'footer-1',
        'description'   => esc_html__('Add widgets here.', 'autohubzambia'),
        'before_widget' => '<div id="%1$s" class="widget %2$s mb-6">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title text-lg font-serif font-semibold mb-3 text-white">',
        'after_title'   => '</h4>',
    ));

    register_sidebar(array(
        'name'          => esc_html__('Footer Widget Area 2', 'autohubzambia'),
        'id'            => 'footer-2',
        'description'   => esc_html__('Add widgets here.', 'autohubzambia'),
        'before_widget' => '<div id="%1$s" class="widget %2$s mb-6">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title text-lg font-serif font-semibold mb-3 text-white">',
        'after_title'   => '</h4>',
    ));

    register_sidebar(array(
        'name'          => esc_html__('Footer Widget Area 3', 'autohubzambia'),
        'id'            => 'footer-3',
        'description'   => esc_html__('Add widgets here.', 'autohubzambia'),
        'before_widget' => '<div id="%1$s" class="widget %2$s mb-6">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title text-lg font-serif font-semibold mb-3 text-white">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'autohub_widgets_init');

// Custom navigation walker for desktop menu
class AutoHub_Walker_Nav_Menu extends Walker_Nav_Menu {
    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;

        $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= '<li' . $id . $class_names .'>';

        $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
        $attributes .= ! empty($item->target)     ? ' target="' . esc_attr($item->target     ) .'"' : '';
        $attributes .= ! empty($item->xfn)        ? ' rel="'    . esc_attr($item->xfn        ) .'"' : '';
        $attributes .= ! empty($item->url)        ? ' href="'   . esc_attr($item->url        ) .'"' : '';

        $item_output = isset($args->before) ? $args->before : '';
        $item_output .= '<a' . $attributes . ' class="text-gray-700 hover:text-primary font-medium transition-colors duration-200">';
        $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
        $item_output .= '</a>';
        $item_output .= isset($args->after) ? $args->after : '';

        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }
}

// Custom navigation walker for mobile menu
class AutoHub_Walker_Mobile_Nav_Menu extends Walker_Nav_Menu {
    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;

        $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= '<li' . $id . $class_names .'>';

        $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
        $attributes .= ! empty($item->target)     ? ' target="' . esc_attr($item->target     ) .'"' : '';
        $attributes .= ! empty($item->xfn)        ? ' rel="'    . esc_attr($item->xfn        ) .'"' : '';
        $attributes .= ! empty($item->url)        ? ' href="'   . esc_attr($item->url        ) .'"' : '';

        $item_output = isset($args->before) ? $args->before : '';
        $item_output .= '<a' . $attributes . ' class="block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 font-medium transition-colors duration-200">';
        $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
        $item_output .= '</a>';
        $item_output .= isset($args->after) ? $args->after : '';

        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }
}

// Custom navigation walker for footer menu
class AutoHub_Walker_Footer_Nav_Menu extends Walker_Nav_Menu {
    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;

        $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= '<li' . $id . $class_names .'>';

        $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
        $attributes .= ! empty($item->target)     ? ' target="' . esc_attr($item->target     ) .'"' : '';
        $attributes .= ! empty($item->xfn)        ? ' rel="'    . esc_attr($item->xfn        ) .'"' : '';
        $attributes .= ! empty($item->url)        ? ' href="'   . esc_attr($item->url        ) .'"' : '';

        $item_output = isset($args->before) ? $args->before : '';
        $item_output .= '<a' . $attributes . ' class="text-gray-300 hover:text-white text-sm transition-colors duration-200">';
        $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
        $item_output .= '</a>';
        $item_output .= isset($args->after) ? $args->after : '';

        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }
}

// Add custom body classes
function autohub_body_classes($classes) {
    // Add class for when there's no sidebar
    if (!is_active_sidebar('sidebar-1')) {
        $classes[] = 'no-sidebar';
    }

    return $classes;
}
add_filter('body_class', 'autohub_body_classes');

// Customize excerpt length
function autohub_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'autohub_excerpt_length');

// Customize excerpt more
function autohub_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'autohub_excerpt_more');

// Add theme customizer options
function autohub_customize_register($wp_customize) {
    // Add theme options section
    $wp_customize->add_section('autohub_theme_options', array(
        'title'    => esc_html__('Theme Options', 'autohubzambia'),
        'priority' => 130,
    ));

    // Add copyright text setting
    $wp_customize->add_setting('autohub_copyright_text', array(
        'default'           => esc_html__('© 2024 AutoHub Zambia. All rights reserved.', 'autohubzambia'),
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('autohub_copyright_text', array(
        'label'   => esc_html__('Copyright Text', 'autohubzambia'),
        'section' => 'autohub_theme_options',
        'type'    => 'text',
    ));
}
add_action('customize_register', 'autohub_customize_register');

// ========================================
// INCLUDE CUSTOM FUNCTIONALITY
// ========================================

/**
 * Include custom post types
 */
require_once get_template_directory() . '/inc/post-types.php';

/**
 * Include custom fields
 */
require_once get_template_directory() . '/inc/custom-fields.php';

/**
 * Include user registration system
 */
require_once get_template_directory() . '/inc/user-registration.php';

/**
 * Include location preloader functions
 */
require_once get_template_directory() . '/inc/location-preloader.php';

/**
 * Modify archive queries for filtering
 */
function autohub_modify_archive_query($query) {
    if (!is_admin() && $query->is_main_query()) {
        
        // Auto Shop filtering by category
        if (is_post_type_archive('auto_shop') && isset($_GET['category'])) {
            $category_slug = sanitize_text_field($_GET['category']);
            // Verify the category exists
            $category_term = get_term_by('slug', $category_slug, 'auto_shop_category');
            if ($category_term) {
                $query->set('tax_query', array(
                    array(
                        'taxonomy' => 'auto_shop_category',
                        'field'    => 'slug',
                        'terms'    => $category_slug,
                    ),
                ));
            }
        }
        
        // Vehicle Professional filtering by type
        if (is_post_type_archive('vehicle_professional')) {
            $meta_query = array();
            
            // Filter by professional type (mechanic or body_specialist)
            if (isset($_GET['type'])) {
                $professional_type = sanitize_text_field($_GET['type']);
                if (in_array($professional_type, ['mechanic', 'body_specialist'])) {
                    $meta_query[] = array(
                        'key' => 'professional_type',
                        'value' => $professional_type,
                        'compare' => '='
                    );
                }
            }
            
            // Filter by specialization
            if (isset($_GET['specialization'])) {
                $specialization = sanitize_text_field($_GET['specialization']);
                $valid_specializations = [
                    // Mechanic specializations
                    'engine_repair', 'transmission_repair', 'brake_systems', 'electrical_systems',
                    'air_conditioning', 'suspension', 'exhaust_systems', 'diagnostics',
                    // Body specialist specializations
                    'panel_beating', 'painting', 'dent_removal', 'window_repair', 
                    'rust_repair', 'collision_repair', 'custom_paint_work', 'scratch_repair'
                ];
                if (in_array($specialization, $valid_specializations)) {
                    $meta_query[] = array(
                        'key' => 'specializations',
                        'value' => str_replace('_', ' ', $specialization),
                        'compare' => 'LIKE'
                    );
                }
            }
            
            // Apply meta query if we have filters
            if (!empty($meta_query)) {
                if (count($meta_query) > 1) {
                    $meta_query['relation'] = 'AND';
                }
                $query->set('meta_query', $meta_query);
            }
        }
    }
}
add_action('pre_get_posts', 'autohub_modify_archive_query');

/**
 * Flush rewrite rules on theme activation to ensure new post types work
 */
function autohub_flush_rewrite_rules() {
    // Make sure post types are registered
    autohub_register_vehicle_professional_post_type();
    // Flush rewrite rules
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'autohub_flush_rewrite_rules');

/**
 * Add custom user roles for business owners
 */
function autohub_add_user_roles() {
    // Add auto shop owner role
    add_role(
        'auto_shop_owner',
        __('Auto Shop Owner', 'autohubzambia'),
        array(
            'read' => true,
            'edit_posts' => false,
            'delete_posts' => false,
            'publish_posts' => false,
            'upload_files' => true,
            // Service center capabilities
            'edit_service_centers' => true,
            'edit_published_service_centers' => true,
            'edit_private_service_centers' => true,
            'delete_service_centers' => true,
            'delete_published_service_centers' => true,
            'delete_private_service_centers' => true,
            'publish_service_centers' => true,
            'read_private_service_centers' => true,
            // Listing capabilities
            'edit_listings' => true,
            'edit_published_listings' => true,
            'edit_private_listings' => true,
            'delete_listings' => true,
            'delete_published_listings' => true,
            'delete_private_listings' => true,
            'publish_listings' => true,
            'read_private_listings' => true,
            // Quote capabilities
            'edit_quotes' => true,
            'edit_published_quotes' => true,
            'edit_private_quotes' => true,
            'delete_quotes' => true,
            'delete_published_quotes' => true,
            'delete_private_quotes' => true,
            'publish_quotes' => true,
            'read_private_quotes' => true,
        )
    );

    // Add consumer role
    add_role(
        'consumer',
        __('Consumer', 'autohubzambia'),
        array(
            'read' => true,
            'edit_posts' => false,
            'delete_posts' => false,
            'publish_posts' => false,
            'upload_files' => true,
            // Request capabilities
            'edit_requests' => true,
            'edit_published_requests' => true,
            'edit_private_requests' => true,
            'delete_requests' => true,
            'delete_published_requests' => true,
            'delete_private_requests' => true,
            'publish_requests' => true,
            'read_private_requests' => true,
            // My Vehicle capabilities
            'edit_my_vehicles' => true,
            'edit_published_my_vehicles' => true,
            'edit_private_my_vehicles' => true,
            'delete_my_vehicles' => true,
            'delete_published_my_vehicles' => true,
            'delete_private_my_vehicles' => true,
            'publish_my_vehicles' => true,
            'read_private_my_vehicles' => true,
        )
    );
}
add_action('after_switch_theme', 'autohub_add_user_roles');

/**
 * Migrate business_owner role to auto_shop_owner
 */
function autohub_migrate_business_owner_role() {
    // Get all users with business_owner role
    $users = get_users(array('role' => 'business_owner'));
    
    foreach ($users as $user) {
        // Remove business_owner role and add auto_shop_owner role
        $user->remove_role('business_owner');
        $user->add_role('auto_shop_owner');
    }
    
    // Remove the old business_owner role
    remove_role('business_owner');
    
    // Mark migration as complete
    update_option('autohub_role_migration_complete', true);
}

// Run migration only once
if (!get_option('autohub_role_migration_complete', false)) {
    add_action('after_switch_theme', 'autohub_migrate_business_owner_role');
}

/**
 * Quote System Functionality
 */

/**
 * Auto-set quote date when quote is saved
 */
function autohub_set_quote_date($post_id) {
    // Only for quote post type
    if (get_post_type($post_id) !== 'quote') {
        return;
    }

    // Only set date if it's not already set
    if (!get_field('quote_date', $post_id)) {
        update_field('quote_date', current_time('Y-m-d H:i:s'), $post_id);
    }

    // Auto-set quote owner to current user if not set
    if (!get_field('quote_owner_id', $post_id)) {
        update_field('quote_owner_id', get_current_user_id(), $post_id);
    }
}
add_action('save_post_quote', 'autohub_set_quote_date');

/**
 * Auto-set request status to open when request is created
 */
function autohub_set_request_status($post_id) {
    // Only for request post type
    if (get_post_type($post_id) !== 'request') {
        return;
    }

    // Only set status if it's not already set
    if (!get_field('request_status', $post_id)) {
        update_field('request_status', 'open', $post_id);
    }
}
add_action('save_post_request', 'autohub_set_request_status');

/**
 * Handle quote acceptance - close request and decline other quotes
 */
function autohub_handle_quote_acceptance($post_id) {
    // Only for quote post type
    if (get_post_type($post_id) !== 'quote') {
        return;
    }

    $quote_status = get_field('quote_status', $post_id);
    
    // If quote is accepted
    if ($quote_status === 'accepted') {
        $request_id = get_field('request_id', $post_id);
        
        if ($request_id) {
            // Close the request
            update_field('request_status', 'closed', $request_id);
            
            // Get all other quotes for this request
            $other_quotes = get_posts(array(
                'post_type' => 'quote',
                'meta_query' => array(
                    array(
                        'key' => 'request_id',
                        'value' => $request_id,
                        'compare' => '='
                    )
                ),
                'exclude' => array($post_id),
                'posts_per_page' => -1
            ));
            
            // Decline all other quotes
            foreach ($other_quotes as $quote) {
                update_field('quote_status', 'declined', $quote->ID);
            }
        }
    }
}
add_action('save_post_quote', 'autohub_handle_quote_acceptance');

/**
 * Get quote count for a request
 */
function autohub_get_request_quote_count($request_id) {
    $quotes = get_posts(array(
        'post_type' => 'quote',
        'meta_query' => array(
            array(
                'key' => 'request_id',
                'value' => $request_id,
                'compare' => '='
            )
        ),
        'posts_per_page' => -1,
        'fields' => 'ids'
    ));
    
    return count($quotes);
}

/**
 * Check if user can submit quote for request
 */
function autohub_can_user_quote_request($request_id, $user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    // Must be logged in
    if (!$user_id) {
        return false;
    }
    
    // Must be auto shop owner
    $user = get_user_by('id', $user_id);
    if (!in_array('auto_shop_owner', $user->roles)) {
        return false;
    }
    
    // Request must be open
    $request_status = get_field('request_status', $request_id);
    if ($request_status !== 'open') {
        return false;
    }
    
    // User shouldn't have already quoted this request
    $existing_quotes = get_posts(array(
        'post_type' => 'quote',
        'meta_query' => array(
            array(
                'key' => 'request_id',
                'value' => $request_id,
                'compare' => '='
            ),
            array(
                'key' => 'quote_owner_id',
                'value' => $user_id,
                'compare' => '='
            )
        ),
        'posts_per_page' => 1,
        'fields' => 'ids'
    ));
    
    return empty($existing_quotes);
}

/**
 * Check if user can accept/decline quotes for request
 */
function autohub_can_user_manage_request_quotes($request_id, $user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    // Must be logged in
    if (!$user_id) {
        return false;
    }
    
    // Must be the request owner
    $request = get_post($request_id);
    return $request && $request->post_author == $user_id;
}

/**
 * AJAX handler for updating quote status
 */
function autohub_update_quote_status() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'update_quote_status')) {
        wp_die('Security check failed');
    }
    
    $quote_id = intval($_POST['quote_id']);
    $status = sanitize_text_field($_POST['status']);
    
    // Validate status
    if (!in_array($status, ['accepted', 'declined'])) {
        wp_send_json_error('Invalid status');
    }
    
    // Get the quote and request
    $quote = get_post($quote_id);
    if (!$quote || $quote->post_type !== 'quote') {
        wp_send_json_error('Quote not found');
    }
    
    $request_id = get_field('request_id', $quote_id);
    if (!$request_id) {
        wp_send_json_error('Request not found');
    }
    
    // Check if user can manage this request's quotes
    if (!autohub_can_user_manage_request_quotes($request_id)) {
        wp_send_json_error('You do not have permission to manage quotes for this request');
    }
    
    // Update quote status
    update_field('quote_status', $status, $quote_id);
    
    // If accepted, handle the acceptance logic
    if ($status === 'accepted') {
        // Close the request
        update_field('request_status', 'closed', $request_id);
        
        // Decline all other quotes for this request
        $other_quotes = get_posts(array(
            'post_type' => 'quote',
            'meta_query' => array(
                array(
                    'key' => 'request_id',
                    'value' => $request_id,
                    'compare' => '='
                )
            ),
            'exclude' => array($quote_id),
            'posts_per_page' => -1
        ));
        
        foreach ($other_quotes as $other_quote) {
            update_field('quote_status', 'declined', $other_quote->ID);
        }
    }
    
    wp_send_json_success('Quote status updated successfully');
}
add_action('wp_ajax_update_quote_status', 'autohub_update_quote_status');
add_action('wp_ajax_nopriv_update_quote_status', 'autohub_update_quote_status');

/**
 * Modify quote archive query
 */
function autohub_modify_quote_archive_query($query) {
    if (!is_admin() && $query->is_main_query() && is_post_type_archive('quote')) {
        $meta_query = array();
        
        // Filter by quote status
        if (isset($_GET['quote_status']) && !empty($_GET['quote_status'])) {
            $meta_query[] = array(
                'key' => 'quote_status',
                'value' => sanitize_text_field($_GET['quote_status']),
                'compare' => '='
            );
        }
        
        // Filter by availability status
        if (isset($_GET['availability_status']) && !empty($_GET['availability_status'])) {
            $meta_query[] = array(
                'key' => 'availability_status',
                'value' => sanitize_text_field($_GET['availability_status']),
                'compare' => '='
            );
        }
        
        // Filter by price range
        if (isset($_GET['price_range']) && !empty($_GET['price_range'])) {
            $price_range = sanitize_text_field($_GET['price_range']);
            
            switch ($price_range) {
                case '0-1000':
                    $meta_query[] = array(
                        'key' => 'quote_price',
                        'value' => array(0, 1000),
                        'type' => 'NUMERIC',
                        'compare' => 'BETWEEN'
                    );
                    break;
                case '1000-5000':
                    $meta_query[] = array(
                        'key' => 'quote_price',
                        'value' => array(1000, 5000),
                        'type' => 'NUMERIC',
                        'compare' => 'BETWEEN'
                    );
                    break;
                case '5000-10000':
                    $meta_query[] = array(
                        'key' => 'quote_price',
                        'value' => array(5000, 10000),
                        'type' => 'NUMERIC',
                        'compare' => 'BETWEEN'
                    );
                    break;
                case '10000+':
                    $meta_query[] = array(
                        'key' => 'quote_price',
                        'value' => 10000,
                        'type' => 'NUMERIC',
                        'compare' => '>'
                    );
                    break;
            }
        }
        
        if (!empty($meta_query)) {
            $query->set('meta_query', $meta_query);
        }
        
        // Handle sorting
        if (isset($_GET['orderby'])) {
            $orderby = sanitize_text_field($_GET['orderby']);
            
            switch ($orderby) {
                case 'price_low':
                    $query->set('meta_key', 'quote_price');
                    $query->set('orderby', 'meta_value_num');
                    $query->set('order', 'ASC');
                    break;
                case 'price_high':
                    $query->set('meta_key', 'quote_price');
                    $query->set('orderby', 'meta_value_num');
                    $query->set('order', 'DESC');
                    break;
            }
        }
    }
}
add_action('pre_get_posts', 'autohub_modify_quote_archive_query');

/**
 * Pre-populate quote fields when creating from request
 */
function autohub_prepopulate_quote_fields($value, $post_id, $field) {
    // Only for new quotes
    if (!$post_id || get_post_type($post_id) !== 'quote') {
        return $value;
    }
    
    // Check if we're creating a new quote from a request
    if (isset($_GET['request_id']) && $field['name'] === 'request_id') {
        $request_id = intval($_GET['request_id']);
        if ($request_id && get_post_type($request_id) === 'request') {
            return $request_id;
        }
    }
    
    return $value;
}
add_filter('acf/load_value', 'autohub_prepopulate_quote_fields', 10, 3);

/**
 * Get page URLs for request/quote system
 */
function autohub_get_page_urls() {
    return array(
        'submit_quote' => get_permalink(267),
        'my_quotes' => get_permalink(273),
        'my_requests' => get_permalink(269),
        'submit_request' => get_permalink(275),
        'browse_quotes' => get_permalink(277),
        'browse_requests' => get_permalink(279)
    );
}

/**
 * Get submit quote URL with request ID
 */
function autohub_get_submit_quote_url($request_id) {
    return add_query_arg('request_id', $request_id, get_permalink(267));
}

/**
 * Get notification counts for current user
 */
function autohub_get_user_notifications() {
    if (!is_user_logged_in()) {
        return array();
    }
    
    $user_id = get_current_user_id();
    $user = wp_get_current_user();
    $notifications = array();
    
    if (in_array('consumer', $user->roles)) {
        // Count pending quotes on user's requests
        $user_requests = get_posts(array(
            'post_type' => 'request',
            'author' => $user_id,
            'posts_per_page' => -1,
            'fields' => 'ids'
        ));
        
        if (!empty($user_requests)) {
            $pending_quotes = get_posts(array(
                'post_type' => 'quote',
                'meta_query' => array(
                    array(
                        'key' => 'request_id',
                        'value' => $user_requests,
                        'compare' => 'IN'
                    ),
                    array(
                        'key' => 'quote_status',
                        'value' => 'pending',
                        'compare' => '='
                    )
                ),
                'posts_per_page' => -1
            ));
            
            $notifications['pending_quotes'] = count($pending_quotes);
        }
    } elseif (in_array('auto_shop_owner', $user->roles)) {
        // Count accepted/declined quotes for shop owner
        $status_updates = get_posts(array(
            'post_type' => 'quote',
            'meta_query' => array(
                array(
                    'key' => 'quote_owner_id',
                    'value' => $user_id,
                    'compare' => '='
                ),
                array(
                    'key' => 'quote_status',
                    'value' => array('accepted', 'declined'),
                    'compare' => 'IN'
                )
            ),
            'date_query' => array(
                array(
                    'after' => '1 week ago'
                )
            ),
            'posts_per_page' => -1
        ));
        
        $notifications['quote_updates'] = count($status_updates);
    }
    
    return $notifications;
}

/**
 * AJAX handler for quote status updates
 */
function autohub_handle_quote_status_update() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'quote_status_update')) {
        wp_die('Security check failed');
    }
    
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_die('You must be logged in');
    }
    
    $quote_id = intval($_POST['quote_id']);
    $new_status = sanitize_text_field($_POST['status']);
    $request_id = intval($_POST['request_id']);
    
    // Verify the quote exists and user owns the request
    $quote = get_post($quote_id);
    $request = get_post($request_id);
    
    if (!$quote || !$request || $request->post_author != get_current_user_id()) {
        wp_die('Invalid request');
    }
    
    // Update quote status
    update_field('quote_status', $new_status, $quote_id);
    
    // Send email notification
    autohub_notify_quote_status_change($quote_id, $new_status);
    
    // If accepted, close the request and decline other quotes
    if ($new_status === 'accepted') {
        update_field('request_status', 'closed', $request_id);
        
        // Decline all other quotes for this request
        $other_quotes = get_posts(array(
            'post_type' => 'quote',
            'meta_query' => array(
                array(
                    'key' => 'request_id',
                    'value' => $request_id,
                    'compare' => '='
                )
            ),
            'exclude' => array($quote_id),
            'posts_per_page' => -1
        ));
        
        foreach ($other_quotes as $other_quote) {
            update_field('quote_status', 'declined', $other_quote->ID);
            // Send notification for declined quotes too
            autohub_notify_quote_status_change($other_quote->ID, 'declined');
        }
    }
    
    wp_die('Success');
}
add_action('wp_ajax_quote_status_update', 'autohub_handle_quote_status_update');

/**
 * Enqueue AJAX script for quote management
 */
function autohub_enqueue_quote_ajax() {
    if (is_page(269) || is_singular('request')) { // My Requests page or single request
        wp_enqueue_script('autohub-quote-ajax', get_template_directory_uri() . '/assets/js/quote-ajax.js', array('jquery'), '1.0.0', true);
        wp_localize_script('autohub-quote-ajax', 'autohub_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('quote_status_update')
        ));
    }
}
add_action('wp_enqueue_scripts', 'autohub_enqueue_quote_ajax');

/**
 * Send email notification when quote is submitted
 */
function autohub_notify_quote_submitted($quote_id) {
    $quote = get_post($quote_id);
    $request_id = get_field('request_id', $quote_id);
    $request = get_post($request_id);
    $request_owner = get_user_by('ID', $request->post_author);
    $quote_owner = get_user_by('ID', $quote->post_author);
    
    if (!$request_owner || !$quote_owner) {
        return;
    }
    
    $subject = 'New Quote Received for Your Part Request';
    $message = sprintf(
        "Hello %s,\n\nYou have received a new quote for your part request: %s\n\nQuote from: %s\nRequest: %s\n\nView and manage your quotes: %s\n\nBest regards,\nAutoHub Zambia Team",
        $request_owner->display_name,
        $request->post_title,
        $quote_owner->display_name,
        get_permalink($request_id),
        get_permalink(269)
    );
    
    wp_mail($request_owner->user_email, $subject, $message);
}

/**
 * Send email notification when quote status changes
 */
function autohub_notify_quote_status_change($quote_id, $status) {
    $quote = get_post($quote_id);
    $request_id = get_field('request_id', $quote_id);
    $request = get_post($request_id);
    $quote_owner = get_user_by('ID', $quote->post_author);
    
    if (!$quote_owner || !$request) {
        return;
    }
    
    $status_text = ucfirst($status);
    $subject = "Your Quote Has Been {$status_text}";
    
    if ($status === 'accepted') {
        $message = sprintf(
            "Congratulations %s!\n\nYour quote for \"%s\" has been accepted.\n\nRequest: %s\nYour Quote: %s\n\nPlease contact the customer to arrange delivery/pickup.\n\nView your quotes: %s\n\nBest regards,\nAutoHub Zambia Team",
            $quote_owner->display_name,
            $request->post_title,
            get_permalink($request_id),
            get_permalink($quote_id),
            get_permalink(273)
        );
    } else {
        $message = sprintf(
            "Hello %s,\n\nYour quote for \"%s\" has been declined.\n\nDon't worry, there are many other opportunities available.\n\nBrowse more requests: %s\nView your quotes: %s\n\nBest regards,\nAutoHub Zambia Team",
            $quote_owner->display_name,
            $request->post_title,
            get_permalink(279),
            get_permalink(273)
        );
    }
    
    wp_mail($quote_owner->user_email, $subject, $message);
}

/**
 * Hook into quote creation and status updates
 */
function autohub_handle_quote_notifications($post_id, $post, $update) {
    if ($post->post_type !== 'quote') {
        return;
    }
    
    // If it's a new quote, send notification
    if (!$update) {
        autohub_notify_quote_submitted($post_id);
    }
}
add_action('wp_insert_post', 'autohub_handle_quote_notifications', 10, 3);

/**
 * Control access to quotes and requests
 */
function autohub_control_post_access($content) {
    global $post;
    
    if (!is_singular(array('quote', 'request')) || !$post) {
        return $content;
    }
    
    // Allow access if user is logged in and is either:
    // 1. The post author
    // 2. For quotes: the request owner
    // 3. Admin
    if (is_user_logged_in()) {
        $current_user_id = get_current_user_id();
        $post_author_id = $post->post_author;
        
        // Admin access
        if (current_user_can('manage_options')) {
            return $content;
        }
        
        // Post author access
        if ($current_user_id == $post_author_id) {
            return $content;
        }
        
        // For quotes, also allow request owner to view
        if ($post->post_type === 'quote') {
            $request_id = get_field('request_id', $post->ID);
            if ($request_id) {
                $request = get_post($request_id);
                if ($request && $request->post_author == $current_user_id) {
                    return $content;
                }
            }
        }
        
        // For requests, allow shop owners to view (for quoting purposes)
        if ($post->post_type === 'request') {
            $user = wp_get_current_user();
            if (in_array('auto_shop_owner', $user->roles)) {
                return $content;
            }
        }
    }
    
    // If not authorized, show access denied message
    return '<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
        <svg class="w-12 h-12 text-yellow-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <h3 class="text-lg font-semibold text-yellow-800 mb-2">Access Restricted</h3>
        <p class="text-yellow-700 mb-4">You need to be logged in to view this content.</p>
        <a href="' . wp_login_url(get_permalink()) . '" class="btn-primary">Sign In</a>
    </div>';
}
add_filter('the_content', 'autohub_control_post_access');


/**
 * Create important pages on theme activation
 */
function autohub_create_important_pages() {
    // Create Business Dashboard page if it doesn't exist
    $business_dashboard_id = get_option('autohub_business_dashboard_page_id', 0);
    
    if (!$business_dashboard_id || !get_post($business_dashboard_id)) {
        $page_data = [
            'post_title' => __('Business Dashboard', 'autohubzambia'),
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_slug' => 'business-dashboard',
            'page_template' => 'page-business-dashboard.php'
        ];
        
        $page_id = wp_insert_post($page_data);
        
        if ($page_id && !is_wp_error($page_id)) {
            update_option('autohub_business_dashboard_page_id', $page_id);
            update_post_meta($page_id, '_wp_page_template', 'page-business-dashboard.php');
        }
    } else {
        // Ensure existing page has correct template
        $current_template = get_post_meta($business_dashboard_id, '_wp_page_template', true);
        if ($current_template !== 'page-business-dashboard.php') {
            update_post_meta($business_dashboard_id, '_wp_page_template', 'page-business-dashboard.php');
        }
    }
    
    // Create registration page if it doesn't exist
    $register_page_id = get_option('autohub_register_page_id', 0);
    
    if (!$register_page_id || !get_post($register_page_id)) {
        $register_page = wp_insert_post(array(
            'post_title' => 'Register',
            'post_content' => '[autohub_registration_form]',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'register'
        ));
        
        if ($register_page && !is_wp_error($register_page)) {
            update_option('autohub_register_page_id', $register_page);
        }
    }
    
    // Create business registration page if it doesn't exist
    $business_register_page_id = get_option('autohub_business_register_page_id', 0);
    
    if (!$business_register_page_id || !get_post($business_register_page_id)) {
        $business_register_page = wp_insert_post(array(
            'post_title' => 'Business Registration',
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_name' => 'business-registration'
        ));
        
        if ($business_register_page && !is_wp_error($business_register_page)) {
            update_option('autohub_business_register_page_id', $business_register_page);
            update_post_meta($business_register_page, '_wp_page_template', 'page-business-registration.php');
        }
    }
}
add_action('after_switch_theme', 'autohub_create_important_pages');

/**
 * Get AutoHub important page IDs
 */
function autohub_get_page_ids() {
    return array(
        'dashboard' => 92, // Consumer Dashboard
        'business_dashboard' => 103, // Business Dashboard (Auto Shop Dashboard)
        'professional_dashboard' => 316, // Vehicle Professional Dashboard
        'register' => 85, // Consumer Registration page (User registration)
        'business_register' => 99, // Business owner registration page
        'about' => 826, // About Us page
        'contact' => get_option('autohub_contact_page_id', 0), // Contact page
        'faqs' => get_option('autohub_faqs_page_id', 0), // FAQs page
        'auto_shops' => 59, // Auto Shops page
        'body_shops' => 61, // Body Shops page
        'garages' => 63, // Garages page
        'mechanics' => 65, // Mechanics page
        'body_specialists' => 67, // Body Specialists page
    );
}

/**
 * Get dashboard page URL
 */
function autohub_get_dashboard_url() {
    $page_ids = autohub_get_page_ids();
    return get_permalink($page_ids['dashboard']);
}

/**
 * Get registration page URL
 */
function autohub_get_register_url() {
    $page_ids = autohub_get_page_ids();
    if ($page_ids['register']) {
        return get_permalink($page_ids['register']);
    }
    return wp_registration_url();
}

/**
 * Get business dashboard URL
 */
function autohub_get_business_dashboard_url() {
    $page_ids = autohub_get_page_ids();
    $url = get_permalink($page_ids['business_dashboard']);
    
    // Debug logging for administrators
    if (current_user_can('administrator')) {
        error_log('AutoHub Debug - Business Dashboard URL: Page ID ' . $page_ids['business_dashboard'] . ' -> ' . $url);
    }
    
    return $url;
}

/**
 * Get vehicle professional dashboard URL
 */
function autohub_get_professional_dashboard_url() {
    $page_ids = autohub_get_page_ids();
    $url = get_permalink($page_ids['professional_dashboard']);
    
    // Debug logging for administrators
    if (current_user_can('administrator')) {
        error_log('AutoHub Debug - Professional Dashboard URL: Page ID ' . $page_ids['professional_dashboard'] . ' -> ' . $url);
    }
    
    return $url;
}

/**
 * Verify business dashboard page setup
 */
function autohub_verify_business_dashboard_setup() {
    $page_id = 103;
    $page = get_post($page_id);
    
    if (!$page) {
        if (current_user_can('administrator')) {
            error_log('AutoHub Error - Business Dashboard page with ID 103 does not exist!');
        }
        return false;
    }
    
    // Check if page has correct template
    $template = get_page_template_slug($page_id);
    if ($template !== 'page-business-dashboard.php') {
        // Try to set the correct template
        update_post_meta($page_id, '_wp_page_template', 'page-business-dashboard.php');
        if (current_user_can('administrator')) {
            error_log('AutoHub Info - Set template for Business Dashboard page ID 103 to page-business-dashboard.php');
        }
    }
    
    if (current_user_can('administrator')) {
        error_log('AutoHub Info - Business Dashboard page verified: ID ' . $page_id . ', Title: "' . $page->post_title . '", Slug: "' . $page->post_name . '", Template: ' . $template);
    }
    
    return true;
}

// Run verification on admin init
add_action('admin_init', 'autohub_verify_business_dashboard_setup');

/**
 * Restrict auto shop owners to only edit their own posts
 */
function autohub_restrict_auto_shop_owner_posts($query) {
    global $pagenow;
    
    // Only apply to admin area
    if (!is_admin()) {
        return;
    }
    
    // Only apply to main query on edit.php (post list) and post.php (edit post)
    if (!$query->is_main_query() || !in_array($pagenow, ['edit.php', 'post.php', 'post-new.php'])) {
        return;
    }
    
    // Only apply to auto shop owners
    if (!current_user_can('auto_shop_owner') || current_user_can('administrator')) {
        return;
    }
    
    // Get current user ID
    $current_user_id = get_current_user_id();
    
    // Restrict to only posts authored by current user
    $query->set('author', $current_user_id);
}
add_action('pre_get_posts', 'autohub_restrict_auto_shop_owner_posts');

/**
 * Prevent auto shop owners from editing posts they don't own
 */
function autohub_prevent_unauthorized_post_edit() {
    global $pagenow, $post;
    
    // Only apply to post edit page
    if ($pagenow !== 'post.php' || !isset($_GET['action']) || $_GET['action'] !== 'edit') {
        return;
    }
    
    // Only apply to auto shop owners
    if (!current_user_can('auto_shop_owner') || current_user_can('administrator')) {
        return;
    }
    
    // Get post ID
    $post_id = isset($_GET['post']) ? intval($_GET['post']) : 0;
    if (!$post_id) {
        return;
    }
    
    // Get post
    $post = get_post($post_id);
    if (!$post) {
        wp_die(__('Post not found.', 'autohubzambia'));
    }
    
    // Check if current user is the author
    $current_user_id = get_current_user_id();
    if ($post->post_author != $current_user_id) {
        wp_die(__('You do not have permission to edit this post.', 'autohubzambia'));
    }
}
add_action('admin_init', 'autohub_prevent_unauthorized_post_edit');

/**
 * Customize admin menu for auto shop owners
 */
function autohub_customize_admin_menu_for_auto_shop_owners() {
    // Only apply to auto shop owners
    if (!current_user_can('auto_shop_owner') || current_user_can('administrator')) {
        return;
    }
    
    // Remove unnecessary menu items
    remove_menu_page('edit-comments.php');
    remove_menu_page('tools.php');
    remove_menu_page('options-general.php');
    
    // Customize post type menu labels
    global $menu, $submenu;
    
    // Update "Posts" to "My Posts" if they have access
    if (isset($menu[5])) {
        $menu[5][0] = __('My Posts', 'autohubzambia');
    }
}
add_action('admin_menu', 'autohub_customize_admin_menu_for_auto_shop_owners', 999);

/**
 * Add "Back to Dashboard" link in admin bar for users
 */
function autohub_add_dashboard_link_to_admin_bar($wp_admin_bar) {
    // Don't show for administrators
    if (current_user_can('administrator')) {
        return;
    }
    
    // Add link for auto shop owners
    if (current_user_can('auto_shop_owner')) {
        $wp_admin_bar->add_node(array(
            'id'    => 'autohub-business-dashboard',
            'title' => __('← Back to Business Dashboard', 'autohubzambia'),
            'href'  => autohub_get_business_dashboard_url(),
            'meta'  => array(
                'class' => 'autohub-dashboard-link autohub-business-dashboard-link'
            )
        ));
    }
    // Add link for vehicle professionals
    elseif (current_user_can('vehicle_professional')) {
        $wp_admin_bar->add_node(array(
            'id'    => 'autohub-professional-dashboard',
            'title' => __('← Back to Professional Dashboard', 'autohubzambia'),
            'href'  => autohub_get_professional_dashboard_url(),
            'meta'  => array(
                'class' => 'autohub-dashboard-link autohub-professional-dashboard-link'
            )
        ));
    }
    // Add link for consumers
    elseif (current_user_can('consumer')) {
        $wp_admin_bar->add_node(array(
            'id'    => 'autohub-consumer-dashboard',
            'title' => __('← Back to My Dashboard', 'autohubzambia'),
            'href'  => autohub_get_dashboard_url(),
            'meta'  => array(
                'class' => 'autohub-dashboard-link autohub-consumer-dashboard-link'
            )
        ));
    }
}
add_action('admin_bar_menu', 'autohub_add_dashboard_link_to_admin_bar', 100);

/**
 * Add custom CSS for admin bar dashboard links
 */
function autohub_admin_bar_css() {
    // Don't show for administrators
    if (current_user_can('administrator')) {
        return;
    }
    
    // Only show for users with dashboard access
    if (!current_user_can('auto_shop_owner') && !current_user_can('consumer') && !current_user_can('vehicle_professional')) {
        return;
    }
    ?>
    <style>
    /* Business Dashboard Link */
    #wp-admin-bar-autohub-business-dashboard .ab-item {
        background-color: #800080 !important;
        color: white !important;
    }
    #wp-admin-bar-autohub-business-dashboard:hover .ab-item {
        background-color: #600060 !important;
    }
    
    /* Professional Dashboard Link */
    #wp-admin-bar-autohub-professional-dashboard .ab-item {
        background-color: #800080 !important;
        color: white !important;
    }
    #wp-admin-bar-autohub-professional-dashboard:hover .ab-item {
        background-color: #600060 !important;
    }
    
    /* Consumer Dashboard Link */
    #wp-admin-bar-autohub-consumer-dashboard .ab-item {
        background-color: #800080 !important;
        color: white !important;
    }
    #wp-admin-bar-autohub-consumer-dashboard:hover .ab-item {
        background-color: #600060 !important;
    }
    </style>
    <?php
}
add_action('admin_head', 'autohub_admin_bar_css');
add_action('wp_head', 'autohub_admin_bar_css');

/**
 * Customize admin footer text for auto shop owners
 */
function autohub_custom_admin_footer_text($text) {
    if (current_user_can('auto_shop_owner') && !current_user_can('administrator')) {
        return __('Welcome to AutoHub Zambia Business Dashboard. Manage your auto shop and listings with ease.', 'autohubzambia');
    }
    return $text;
}
add_filter('admin_footer_text', 'autohub_custom_admin_footer_text');

/**
 * Sanitize and format part numbers
 */
function autohub_sanitize_part_number($part_number) {
    if (empty($part_number)) {
        return '';
    }
    
    // Remove extra spaces and convert to uppercase for consistency
    $part_number = strtoupper(trim($part_number));
    
    // Remove any characters that aren't alphanumeric, hyphens, or underscores
    $part_number = preg_replace('/[^A-Z0-9\-_]/', '', $part_number);
    
    return $part_number;
}

/**
 * Save sanitized part number when listing is saved
 */
function autohub_save_listing_part_number($post_id) {
    // Only process listing post type
    if (get_post_type($post_id) !== 'listing') {
        return;
    }
    
    // Skip if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    // Check user permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // Get the part number from ACF/SCF
    $part_number = get_field('part_number', $post_id);
    
    if (!empty($part_number)) {
        // Sanitize and save the part number
        $sanitized_part_number = autohub_sanitize_part_number($part_number);
        update_field('part_number', $sanitized_part_number, $post_id);
    }
}
add_action('save_post', 'autohub_save_listing_part_number');

/**
 * Add part number search functionality
 */
function autohub_search_listings_by_part_number($query) {
    // Only modify search queries for listings
    if (!is_admin() || !$query->is_search() || $query->get('post_type') !== 'listing') {
        return;
    }
    
    $search_term = $query->get('s');
    if (empty($search_term)) {
        return;
    }
    
    // Add meta query to search part numbers and brand names
    $meta_query = array(
        'relation' => 'OR',
        array(
            'key' => 'part_number',
            'value' => $search_term,
            'compare' => 'LIKE'
        ),
        array(
            'key' => 'brand_name',
            'value' => $search_term,
            'compare' => 'LIKE'
        ),
        array(
            'key' => 'part_name',
            'value' => $search_term,
            'compare' => 'LIKE'
        )
    );
    
    $query->set('meta_query', $meta_query);
}
add_action('pre_get_posts', 'autohub_search_listings_by_part_number');

/**
 * Add part number and brand columns to listings admin list
 */
function autohub_add_listing_admin_columns($columns) {
    // Insert part number and brand columns after title
    $new_columns = array();
    foreach ($columns as $key => $value) {
        $new_columns[$key] = $value;
        if ($key === 'title') {
            $new_columns['part_number'] = __('Part Number', 'autohubzambia');
            $new_columns['brand_name'] = __('Brand', 'autohubzambia');
        }
    }
    return $new_columns;
}
add_filter('manage_listing_posts_columns', 'autohub_add_listing_admin_columns');

/**
 * Display part number and brand in admin columns
 */
function autohub_display_listing_admin_columns($column, $post_id) {
    switch ($column) {
        case 'part_number':
            $part_number = get_post_meta($post_id, 'part_number', true);
            if ($part_number) {
                echo '<code class="bg-gray-100 px-2 py-1 rounded text-xs">' . esc_html($part_number) . '</code>';
            } else {
                echo '<span class="text-gray-400">—</span>';
            }
            break;
            
        case 'brand_name':
            $brand_name = get_post_meta($post_id, 'brand_name', true);
            if ($brand_name) {
                echo '<strong>' . esc_html($brand_name) . '</strong>';
            } else {
                echo '<span class="text-gray-400">—</span>';
            }
            break;
    }
}
add_action('manage_listing_posts_custom_column', 'autohub_display_listing_admin_columns', 10, 2);

/**
 * Make part number and brand columns sortable
 */
function autohub_make_listing_columns_sortable($columns) {
    $columns['part_number'] = 'part_number';
    $columns['brand_name'] = 'brand_name';
    return $columns;
}
add_filter('manage_edit-listing_sortable_columns', 'autohub_make_listing_columns_sortable');

/**
 * Handle sorting by part number and brand
 */
function autohub_handle_listing_column_sorting($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }
    
    $orderby = $query->get('orderby');
    
    if ($orderby === 'part_number') {
        $query->set('meta_key', 'part_number');
        $query->set('orderby', 'meta_value');
    } elseif ($orderby === 'brand_name') {
        $query->set('meta_key', 'brand_name');
        $query->set('orderby', 'meta_value');
    }
}
add_action('pre_get_posts', 'autohub_handle_listing_column_sorting');

/**
 * Get business registration page URL
 */
function autohub_get_business_register_url() {
    $page_ids = autohub_get_page_ids();
    if ($page_ids['business_register']) {
        return get_permalink($page_ids['business_register']);
    }
    return home_url('/business-registration/');
}

/**
 * Protect important pages from deletion
 */
function autohub_protect_important_pages($post_id) {
    $protected_pages = autohub_get_page_ids();
    
    if (in_array($post_id, $protected_pages)) {
        wp_die(
            __('This page is protected and cannot be deleted as it is essential for the AutoHub theme functionality.', 'autohubzambia'),
            __('Page Protected', 'autohubzambia'),
            array('back_link' => true)
        );
    }
}
add_action('wp_trash_post', 'autohub_protect_important_pages');
add_action('before_delete_post', 'autohub_protect_important_pages');

/**
 * Handle auto shop creation from business dashboard
 */
function autohub_handle_create_auto_shop() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['auto_shop_nonce'], 'create_auto_shop')) {
        wp_die(__('Security check failed', 'autohubzambia'));
    }
    
    // Check if user is logged in and has permission
    if (!is_user_logged_in() || !current_user_can('edit_auto_shops')) {
        wp_die(__('You do not have permission to create auto shops', 'autohubzambia'));
    }
    
    $user_id = get_current_user_id();
    
    // Check if user already has an auto shop
    $existing_shop = get_posts([
        'post_type' => 'auto_shop',
        'author' => $user_id,
        'posts_per_page' => 1,
        'post_status' => ['publish', 'pending', 'draft']
    ]);
    
    if (!empty($existing_shop)) {
        wp_redirect(add_query_arg('error', 'already_exists', wp_get_referer()));
        exit;
    }
    
    // Sanitize and validate input
    $auto_shop_name = sanitize_text_field($_POST['auto_shop_name']);
    $auto_shop_categories = isset($_POST['auto_shop_categories']) ? array_map('intval', $_POST['auto_shop_categories']) : array();
    $province = intval($_POST['province']);
    $city = intval($_POST['city']);
    $physical_address = sanitize_textarea_field($_POST['physical_address']);
    $phone = sanitize_text_field($_POST['phone']);
    $email = sanitize_email($_POST['email']);
    $excerpt = sanitize_textarea_field($_POST['excerpt']);
    $description = wp_kses_post($_POST['description']);
    $services = sanitize_textarea_field($_POST['services']);
    
    // Create the auto shop post
    $post_data = [
        'post_title' => $auto_shop_name,
        'post_content' => $description,
        'post_excerpt' => $excerpt,
        'post_type' => 'auto_shop',
        'post_status' => 'pending', // Require approval
        'post_author' => $user_id
    ];
    
    $post_id = wp_insert_post($post_data);
    
    if (is_wp_error($post_id)) {
        wp_redirect(add_query_arg('error', 'creation_failed', wp_get_referer()));
        exit;
    }
    
    // Save meta data
    update_post_meta($post_id, 'business_name', $auto_shop_name);
    update_post_meta($post_id, 'province_link', $province);
    update_post_meta($post_id, 'city_link', $city);
    update_post_meta($post_id, 'physical_address', $physical_address);
    update_post_meta($post_id, 'phone', $phone);
    update_post_meta($post_id, 'email', $email);
    update_post_meta($post_id, 'services', $services);
    
    // Assign auto shop categories
    if (!empty($auto_shop_categories)) {
        wp_set_post_terms($post_id, $auto_shop_categories, 'auto_shop_category');
    }
    
    // Handle file uploads
    if (!empty($_FILES['logo_image']['name'])) {
        $logo_upload = autohub_handle_image_upload('logo_image', $post_id);
        if ($logo_upload && !is_wp_error($logo_upload)) {
            update_post_meta($post_id, 'logo_image', $logo_upload['url']);
        }
    }
    
    if (!empty($_FILES['header_image']['name'])) {
        $header_upload = autohub_handle_image_upload('header_image', $post_id);
        if ($header_upload && !is_wp_error($header_upload)) {
            update_post_meta($post_id, 'header_image', $header_upload['url']);
        }
    }
    
    // Redirect with success message
    wp_redirect(add_query_arg('success', 'auto_shop_created', wp_get_referer()));
    exit;
}
add_action('admin_post_create_auto_shop', 'autohub_handle_create_auto_shop');

/**
 * AJAX handler to get cities by province
 */
function autohub_get_cities_by_province() {
    $province_id = intval($_POST['province_id'] ?? $_GET['province_id'] ?? 0);
    
    if (!$province_id) {
        wp_send_json_error('Invalid province ID');
    }
    
    // Try both field names for compatibility
    $cities = get_posts([
        'post_type' => 'city_town',
        'posts_per_page' => -1,
        'meta_query' => [
            'relation' => 'OR',
            [
                'key' => 'province_link',
                'value' => $province_id,
                'compare' => '='
            ],
            [
                'key' => 'parent_province',
                'value' => $province_id,
                'compare' => 'LIKE'
            ]
        ],
        'orderby' => 'title',
        'order' => 'ASC'
    ]);
    
    // Format response for frontend
    $response = array();
    foreach ($cities as $city) {
        $response[] = array(
            'id' => $city->ID,
            'title' => $city->post_title
        );
    }
    
    wp_send_json_success($response);
}
add_action('wp_ajax_get_cities_by_province', 'autohub_get_cities_by_province');
add_action('wp_ajax_nopriv_get_cities_by_province', 'autohub_get_cities_by_province');

/**
 * AJAX handler to get categories by city for auto shops filtering
 */
function autohub_get_categories_by_city() {
    $city_id = isset($_POST['city_id']) ? intval($_POST['city_id']) : 0;
    
    global $wpdb;
    
    if ($city_id) {
        // Get categories with counts for specific city
        // Use LIKE with serialized value to match the main query approach
        $categories_with_counts = $wpdb->get_results($wpdb->prepare("
            SELECT 
                t.term_id,
                t.name,
                t.slug,
                COUNT(asp.ID) as auto_shop_count
            FROM {$wpdb->terms} t
            INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id AND tt.taxonomy = 'auto_shop_category'
            INNER JOIN {$wpdb->term_relationships} tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
            INNER JOIN {$wpdb->posts} asp ON tr.object_id = asp.ID AND asp.post_type = 'auto_shop' AND asp.post_status = 'publish'
            INNER JOIN {$wpdb->postmeta} pm_city ON asp.ID = pm_city.post_id 
                AND pm_city.meta_key = 'city_link' 
                AND pm_city.meta_value LIKE %s
            GROUP BY t.term_id, t.name, t.slug
            HAVING auto_shop_count > 0
            ORDER BY t.name ASC
        ", '%' . serialize(strval($city_id)) . '%'));
        
        $total_count = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(asp.ID)
            FROM {$wpdb->posts} asp
            INNER JOIN {$wpdb->postmeta} pm_city ON asp.ID = pm_city.post_id 
                AND pm_city.meta_key = 'city_link' 
                AND pm_city.meta_value LIKE %s
            WHERE asp.post_type = 'auto_shop' AND asp.post_status = 'publish'
        ", '%' . serialize(strval($city_id)) . '%'));
    } else {
        // Get all categories with counts (no city filter)
        $categories_with_counts = $wpdb->get_results("
            SELECT 
                t.term_id,
                t.name,
                t.slug,
                COUNT(asp.ID) as auto_shop_count
            FROM {$wpdb->terms} t
            INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id AND tt.taxonomy = 'auto_shop_category'
            INNER JOIN {$wpdb->term_relationships} tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
            INNER JOIN {$wpdb->posts} asp ON tr.object_id = asp.ID AND asp.post_type = 'auto_shop' AND asp.post_status = 'publish'
            GROUP BY t.term_id, t.name, t.slug
            HAVING auto_shop_count > 0
            ORDER BY t.name ASC
        ");
        
        $total_count = wp_count_posts('auto_shop')->publish;
    }
    
    // Format response
    $response = array(
        'categories' => array(),
        'total_count' => $total_count
    );
    
    foreach ($categories_with_counts as $category) {
        $response['categories'][] = array(
            'term_id' => $category->term_id,
            'name' => $category->name,
            'slug' => $category->slug,
            'count' => $category->auto_shop_count
        );
    }
    
    wp_send_json_success($response);
}
add_action('wp_ajax_get_categories_by_city', 'autohub_get_categories_by_city');
add_action('wp_ajax_nopriv_get_categories_by_city', 'autohub_get_categories_by_city');

/**
 * AJAX handler to get professional types by city for vehicle professionals filtering
 */
function autohub_get_professional_types_by_city() {
    $city_id = isset($_POST['city_id']) ? intval($_POST['city_id']) : 0;
    
    global $wpdb;
    
    if ($city_id) {
        // Get professional types with counts for specific city
        // Handle both serialized arrays and direct values
        $types_with_counts = $wpdb->get_results($wpdb->prepare("
            SELECT 
                t.term_id,
                t.name,
                t.slug,
                COUNT(vp.ID) as professional_count
            FROM {$wpdb->terms} t
            INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id AND tt.taxonomy = 'professional_type'
            INNER JOIN {$wpdb->term_relationships} tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
            INNER JOIN {$wpdb->posts} vp ON tr.object_id = vp.ID AND vp.post_type = 'vehicle_professional' AND vp.post_status = 'publish'
            INNER JOIN {$wpdb->postmeta} pm_city ON vp.ID = pm_city.post_id 
                AND pm_city.meta_key = 'city_link' 
                AND (
                    pm_city.meta_value LIKE %s
                    OR pm_city.meta_value = %s
                )
            GROUP BY t.term_id, t.name, t.slug
            HAVING professional_count > 0
            ORDER BY t.name ASC
        ", '%"' . $city_id . '"%', $city_id));
        
        $total_count = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(vp.ID)
            FROM {$wpdb->posts} vp
            INNER JOIN {$wpdb->postmeta} pm_city ON vp.ID = pm_city.post_id 
                AND pm_city.meta_key = 'city_link' 
                AND (
                    pm_city.meta_value LIKE %s
                    OR pm_city.meta_value = %s
                )
            WHERE vp.post_type = 'vehicle_professional' AND vp.post_status = 'publish'
        ", '%"' . $city_id . '"%', $city_id));
    } else {
        // Get all professional types with counts (no city filter)
        $types_with_counts = $wpdb->get_results("
            SELECT 
                t.term_id,
                t.name,
                t.slug,
                COUNT(vp.ID) as professional_count
            FROM {$wpdb->terms} t
            INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id AND tt.taxonomy = 'professional_type'
            INNER JOIN {$wpdb->term_relationships} tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
            INNER JOIN {$wpdb->posts} vp ON tr.object_id = vp.ID AND vp.post_type = 'vehicle_professional' AND vp.post_status = 'publish'
            GROUP BY t.term_id, t.name, t.slug
            HAVING professional_count > 0
            ORDER BY t.name ASC
        ");
        
        $total_count = wp_count_posts('vehicle_professional')->publish;
    }
    
    // Format response
    $response = array(
        'types' => array(),
        'total_count' => $total_count
    );
    
    foreach ($types_with_counts as $type) {
        $response['types'][] = array(
            'term_id' => $type->term_id,
            'name' => $type->name,
            'slug' => $type->slug,
            'count' => $type->professional_count
        );
    }
    
    wp_send_json_success($response);
}
add_action('wp_ajax_get_professional_types_by_city', 'autohub_get_professional_types_by_city');
add_action('wp_ajax_nopriv_get_professional_types_by_city', 'autohub_get_professional_types_by_city');

/**
 * Handle image upload for service centers and listings
 */
function autohub_handle_image_upload($file_key, $post_id) {
    if (!function_exists('wp_handle_upload')) {
        require_once(ABSPATH . 'wp-admin/includes/file.php');
    }
    
    $uploadedfile = $_FILES[$file_key];
    $upload_overrides = ['test_form' => false];
    
    $movefile = wp_handle_upload($uploadedfile, $upload_overrides);
    
    if ($movefile && !isset($movefile['error'])) {
        // Create attachment
        $attachment = [
            'post_mime_type' => $movefile['type'],
            'post_title' => sanitize_file_name($movefile['file']),
            'post_content' => '',
            'post_status' => 'inherit'
        ];
        
        $attach_id = wp_insert_attachment($attachment, $movefile['file'], $post_id);
        
        if (!is_wp_error($attach_id)) {
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            $attach_data = wp_generate_attachment_metadata($attach_id, $movefile['file']);
            wp_update_attachment_metadata($attach_id, $attach_data);
            
            return [
                'url' => $movefile['url'],
                'attachment_id' => $attach_id
            ];
        }
    }
    
    return false;
}

/**
 * Add admin notice for missing important pages
 */
function autohub_check_important_pages() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $page_ids = autohub_get_page_ids();
    $missing_pages = array();
    
    foreach ($page_ids as $key => $page_id) {
        if ($page_id && !get_post($page_id)) {
            $missing_pages[] = $key;
        }
    }
    
    if (!empty($missing_pages)) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>AutoHub Theme:</strong> Some important pages are missing: ' . implode(', ', $missing_pages) . '. Please recreate them for full functionality.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'autohub_check_important_pages');



/**
 * Two-Factor Authentication System
 */

/**
 * Generate 2FA code
 */
function autohub_generate_2fa_code() {
    return sprintf('%06d', wp_rand(100000, 999999));
}

/**
 * Send 2FA code via email
 */
function autohub_send_2fa_code($user_id, $code) {
    $user = get_user_by('ID', $user_id);
    if (!$user) {
        return false;
    }

    $subject = __('AutoHub Zambia - Security Code', 'autohubzambia');
    $message = sprintf(
        __('Hello %s,

Your security code for AutoHub Zambia is: %s

This code will expire in 10 minutes. If you did not request this code, please ignore this email.

For your security, never share this code with anyone.

Best regards,
AutoHub Zambia Team', 'autohubzambia'),
        $user->display_name,
        $code
    );

    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: AutoHub Zambia <<EMAIL>>'
    );

    // HTML version of the email
    $html_message = '
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="background: linear-gradient(135deg, #800080, #008000); padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 24px;">AutoHub Zambia</h1>
            <p style="color: white; margin: 5px 0 0 0; opacity: 0.9;">Security Code</p>
        </div>
        
        <div style="padding: 30px; background: #f9f9f9;">
            <h2 style="color: #800080; margin-top: 0;">Hello ' . esc_html($user->display_name) . ',</h2>
            
            <p>Your security code for AutoHub Zambia is:</p>
            
            <div style="background: white; border: 2px solid #800080; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
                <div style="font-size: 32px; font-weight: bold; color: #800080; letter-spacing: 4px;">' . $code . '</div>
            </div>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; color: #856404;"><strong>Important:</strong></p>
                <ul style="margin: 10px 0 0 0; color: #856404;">
                    <li>This code will expire in <strong>10 minutes</strong></li>
                    <li>Never share this code with anyone</li>
                    <li>If you did not request this code, please ignore this email</li>
                </ul>
            </div>
            
            <p>If you have any questions, please contact our support team.</p>
            
            <p style="margin-top: 30px;">
                Best regards,<br>
                <strong>AutoHub Zambia Team</strong>
            </p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 12px;">
            <p style="margin: 0;">© ' . date('Y') . ' AutoHub Zambia. All rights reserved.</p>
        </div>
    </div>';

    return wp_mail($user->user_email, $subject, $html_message, $headers);
}

/**
 * Store 2FA code for user
 */
function autohub_store_2fa_code($user_id, $code) {
    $expiry = time() + (10 * 60); // 10 minutes from now
    update_user_meta($user_id, '_autohub_2fa_code', $code);
    update_user_meta($user_id, '_autohub_2fa_code_expiry', $expiry);
    update_user_meta($user_id, '_autohub_2fa_attempts', 0);
}

/**
 * Verify 2FA code
 */
function autohub_verify_2fa_code($user_id, $submitted_code) {
    $stored_code = get_user_meta($user_id, '_autohub_2fa_code', true);
    $expiry = get_user_meta($user_id, '_autohub_2fa_code_expiry', true);
    $attempts = (int) get_user_meta($user_id, '_autohub_2fa_attempts', true);

    // Check if too many attempts
    if ($attempts >= 3) {
        return array('success' => false, 'message' => __('Too many failed attempts. Please request a new code.', 'autohubzambia'));
    }

    // Check if code exists and hasn't expired
    if (!$stored_code || !$expiry || time() > $expiry) {
        return array('success' => false, 'message' => __('Code has expired. Please request a new code.', 'autohubzambia'));
    }

    // Verify code
    if ($submitted_code === $stored_code) {
        // Clear the code after successful verification
        autohub_clear_2fa_code($user_id);
        return array('success' => true, 'message' => __('Code verified successfully.', 'autohubzambia'));
    } else {
        // Increment attempts
        update_user_meta($user_id, '_autohub_2fa_attempts', $attempts + 1);
        return array('success' => false, 'message' => __('Invalid code. Please try again.', 'autohubzambia'));
    }
}

/**
 * Clear 2FA code
 */
function autohub_clear_2fa_code($user_id) {
    delete_user_meta($user_id, '_autohub_2fa_code');
    delete_user_meta($user_id, '_autohub_2fa_code_expiry');
    delete_user_meta($user_id, '_autohub_2fa_attempts');
}

/**
 * Check if user has 2FA enabled
 */
function autohub_is_2fa_enabled($user_id) {
    return get_user_meta($user_id, '_autohub_2fa_enabled', true) === '1';
}

/**
 * Enable/Disable 2FA for user
 */
function autohub_toggle_2fa($user_id, $enable = true) {
    if ($enable) {
        update_user_meta($user_id, '_autohub_2fa_enabled', '1');
    } else {
        delete_user_meta($user_id, '_autohub_2fa_enabled');
        autohub_clear_2fa_code($user_id);
    }
}

/**
 * Handle 2FA AJAX requests
 */
function autohub_handle_2fa_ajax() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'autohub_2fa_nonce')) {
        wp_die('Security check failed');
    }

    $action = sanitize_text_field($_POST['action_type']);
    $user_id = get_current_user_id();

    if (!$user_id) {
        wp_send_json_error('User not logged in');
    }

    switch ($action) {
        case 'enable_2fa':
            // Generate and send code to enable 2FA
            $code = autohub_generate_2fa_code();
            autohub_store_2fa_code($user_id, $code);
            
            if (autohub_send_2fa_code($user_id, $code)) {
                wp_send_json_success(array(
                    'message' => __('Verification code sent to your email. Please check your inbox.', 'autohubzambia'),
                    'step' => 'verify'
                ));
            } else {
                wp_send_json_error(__('Failed to send verification code. Please try again.', 'autohubzambia'));
            }
            break;

        case 'verify_enable_2fa':
            $code = sanitize_text_field($_POST['code']);
            $result = autohub_verify_2fa_code($user_id, $code);
            
            if ($result['success']) {
                autohub_toggle_2fa($user_id, true);
                wp_send_json_success(array(
                    'message' => __('Two-factor authentication has been enabled successfully!', 'autohubzambia'),
                    'step' => 'complete'
                ));
            } else {
                wp_send_json_error($result['message']);
            }
            break;

        case 'disable_2fa':
            if (!autohub_is_2fa_enabled($user_id)) {
                wp_send_json_error('2FA is not enabled for your account');
            }
            
            // Verify current password
            $current_password = sanitize_text_field($_POST['current_password']);
            if (empty($current_password)) {
                wp_send_json_error('Current password is required to disable 2FA');
            }
            
            $user = get_user_by('ID', $user_id);
            if (!wp_check_password($current_password, $user->user_pass, $user_id)) {
                wp_send_json_error('Incorrect password. Please enter your current password to disable 2FA.');
            }
            
            // Disable 2FA
            autohub_toggle_2fa($user_id, false);
            
            // Log the security event
            update_user_meta($user_id, '_autohub_2fa_disabled_at', current_time('mysql'));
            update_user_meta($user_id, '_autohub_2fa_disabled_ip', $_SERVER['REMOTE_ADDR']);
            
            // Send security notification email
            autohub_send_2fa_disabled_notification($user_id);
            
            wp_send_json_success(array(
                'message' => 'Two-factor authentication has been disabled for your account. A security notification has been sent to your email.'
            ));
            break;

        case 'send_test_code':
            if (!autohub_is_2fa_enabled($user_id)) {
                wp_send_json_error(__('2FA is not enabled for your account.', 'autohubzambia'));
            }
            
            $code = autohub_generate_2fa_code();
            autohub_store_2fa_code($user_id, $code);
            
            if (autohub_send_2fa_code($user_id, $code)) {
                wp_send_json_success(array(
                    'message' => __('Test code sent to your email.', 'autohubzambia')
                ));
            } else {
                wp_send_json_error(__('Failed to send test code.', 'autohubzambia'));
            }
            break;

        default:
            wp_send_json_error('Invalid action');
    }
}
add_action('wp_ajax_autohub_2fa', 'autohub_handle_2fa_ajax');

/**
 * Login 2FA verification
 */
function autohub_login_2fa_check($user, $username, $password) {
    // Skip if not a valid user or if 2FA is not enabled
    if (is_wp_error($user) || !autohub_is_2fa_enabled($user->ID)) {
        return $user;
    }

    // Generate and send 2FA code
    $code = autohub_generate_2fa_code();
    autohub_store_2fa_code($user->ID, $code);
    
    if (autohub_send_2fa_code($user->ID, $code)) {
        // Store user data in session for 2FA verification
        if (!session_id()) {
            session_start();
        }
        $_SESSION['autohub_2fa_user_id'] = $user->ID;
        $_SESSION['autohub_2fa_pending'] = true;
        
        // Create 2FA verification page if it doesn't exist
        $verification_page = get_page_by_path('2fa-verify');
        if (!$verification_page) {
            $page_id = wp_insert_post(array(
                'post_title' => '2FA Verification',
                'post_content' => '',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_name' => '2fa-verify',
                'page_template' => 'page-2fa-verify.php'
            ));
            
            if ($page_id && !is_wp_error($page_id)) {
                update_post_meta($page_id, '_wp_page_template', 'page-2fa-verify.php');
            }
        }
        
        // Store redirect URL in session if provided
        if (isset($_POST['redirect_to']) && !empty($_POST['redirect_to'])) {
            $_SESSION['autohub_2fa_redirect'] = $_POST['redirect_to'];
        }
        
        // Redirect to 2FA verification page
        wp_redirect(home_url('/2fa-verify/'));
        exit;
    }

    return $user;
}
add_filter('authenticate', 'autohub_login_2fa_check', 30, 3);

/**
 * Update last login time
 */
function autohub_update_last_login($user_login, $user) {
    update_user_meta($user->ID, 'last_login', current_time('mysql'));
}
add_action('wp_login', 'autohub_update_last_login', 10, 2);

/**
 * Redirect users to appropriate dashboard after login
 */
function autohub_login_redirect($redirect_to, $request, $user) {
    // Check if user is valid and has roles
    if (isset($user->roles) && is_array($user->roles)) {
        // Redirect consumers to consumer dashboard
        if (in_array('consumer', $user->roles)) {
            return autohub_get_dashboard_url();
        }
        // Redirect auto shop owners to business dashboard
        if (in_array('auto_shop_owner', $user->roles)) {
            return autohub_get_business_dashboard_url();
        }
    }
    
    return $redirect_to;
}
add_filter('login_redirect', 'autohub_login_redirect', 10, 3);

/**
 * Handle login 2FA verification AJAX
 */
function autohub_handle_login_2fa_verification() {
    // Start session if not already started
    if (!session_id()) {
        session_start();
    }
    
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'autohub_login_2fa_nonce')) {
        wp_send_json_error('Security check failed');
    }
    
    // Check if 2FA is pending
    if (!isset($_SESSION['autohub_2fa_pending']) || !isset($_SESSION['autohub_2fa_user_id'])) {
        wp_send_json_error('No 2FA verification pending');
    }
    
    $user_id = $_SESSION['autohub_2fa_user_id'];
    $code = sanitize_text_field($_POST['code']);
    
    // Verify the code
    $result = autohub_verify_2fa_code($user_id, $code);
    
    if ($result['success']) {
        // Clear session data
        unset($_SESSION['autohub_2fa_pending']);
        unset($_SESSION['autohub_2fa_user_id']);
        
        // Log the user in
        wp_set_current_user($user_id);
        wp_set_auth_cookie($user_id);
        
        // Update last login
        update_user_meta($user_id, 'last_login', current_time('mysql'));
        
        // Get user to check role
        $user = get_user_by('ID', $user_id);
        
        // Determine redirect URL based on user role
        $redirect_url = home_url(); // Default fallback
        
        if ($user && in_array('consumer', $user->roles)) {
            $redirect_url = autohub_get_dashboard_url();
        } elseif ($user && in_array('auto_shop_owner', $user->roles)) {
            $redirect_url = autohub_get_business_dashboard_url();
        }
        
        // Check if there was a specific redirect requested
        if (isset($_SESSION['autohub_2fa_redirect']) && !empty($_SESSION['autohub_2fa_redirect'])) {
            $requested_redirect = $_SESSION['autohub_2fa_redirect'];
            unset($_SESSION['autohub_2fa_redirect']);
            
            // Only use requested redirect if it's safe and for appropriate users going to dashboard-related pages
            if ($user && 
                ((in_array('consumer', $user->roles) && 
                  (strpos($requested_redirect, 'my-account') !== false || 
                   strpos($requested_redirect, 'dashboard') !== false)) ||
                 (in_array('auto_shop_owner', $user->roles) && 
                  strpos($requested_redirect, 'business-dashboard') !== false))) {
                $redirect_url = $requested_redirect;
            }
        }
        
        wp_send_json_success(array(
            'message' => 'Login successful',
            'redirect_url' => $redirect_url,
            'debug' => array(
                'user_roles' => $user->roles,
                'dashboard_url' => autohub_get_dashboard_url(),
                'business_dashboard_url' => autohub_get_business_dashboard_url(),
                'final_redirect' => $redirect_url
            )
        ));
    } else {
        wp_send_json_error($result['message']);
    }
}
add_action('wp_ajax_nopriv_autohub_verify_login_2fa', 'autohub_handle_login_2fa_verification');

/**
 * Handle resend login 2FA code AJAX
 */
function autohub_handle_resend_login_2fa() {
    // Start session if not already started
    if (!session_id()) {
        session_start();
    }
    
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'autohub_login_2fa_nonce')) {
        wp_send_json_error('Security check failed');
    }
    
    // Check if 2FA is pending
    if (!isset($_SESSION['autohub_2fa_pending']) || !isset($_SESSION['autohub_2fa_user_id'])) {
        wp_send_json_error('No 2FA verification pending');
    }
    
    $user_id = $_SESSION['autohub_2fa_user_id'];
    
    // Generate and send new code
    $code = autohub_generate_2fa_code();
    autohub_store_2fa_code($user_id, $code);
    
    if (autohub_send_2fa_code($user_id, $code)) {
        wp_send_json_success(array(
            'message' => 'New verification code sent to your email'
        ));
    } else {
        wp_send_json_error('Failed to send verification code');
    }
}
add_action('wp_ajax_nopriv_autohub_resend_login_2fa', 'autohub_handle_resend_login_2fa');

/**
 * Handle Add Vehicle AJAX Request
 */
function autohub_handle_add_vehicle() {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error('You must be logged in to add a vehicle.');
        return;
    }

    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'add_vehicle_nonce')) {
        wp_send_json_error('Security check failed.');
        return;
    }

    // Get current user
    $current_user = wp_get_current_user();
    $user_id = $current_user->ID;

    // Sanitize form data
    $vehicle_name = sanitize_text_field($_POST['vehicle_name']);
    $make = sanitize_text_field($_POST['make']);
    $model = sanitize_text_field($_POST['model']);
    $year = sanitize_text_field($_POST['year']);
    $color = sanitize_text_field($_POST['color']);
    $mileage = sanitize_text_field($_POST['mileage']);
    $fuel_type = sanitize_text_field($_POST['fuel_type']);
    $transmission = sanitize_text_field($_POST['transmission']);
    $vehicle_description = sanitize_textarea_field($_POST['vehicle_description']);

    // Validate required fields
    if (empty($vehicle_name) || empty($make) || empty($model) || empty($year)) {
        wp_send_json_error('Please fill in all required fields.');
        return;
    }

    // Create the vehicle post
    $vehicle_data = array(
        'post_title' => $vehicle_name,
        'post_content' => $vehicle_description,
        'post_status' => 'publish',
        'post_type' => 'my_vehicle',
        'post_author' => $user_id,
    );

    $vehicle_id = wp_insert_post($vehicle_data);

    if (is_wp_error($vehicle_id)) {
        wp_send_json_error('Failed to create vehicle. Please try again.');
        return;
    }

    // Add meta fields
    update_post_meta($vehicle_id, 'make', $make);
    update_post_meta($vehicle_id, 'model', $model);
    update_post_meta($vehicle_id, 'year', $year);
    
    // Debug: Log the saved data
    error_log("Vehicle created - ID: $vehicle_id, Make: $make, Model: $model, Year: $year");
    
    if (!empty($color)) {
        update_post_meta($vehicle_id, 'color', $color);
    }
    
    if (!empty($mileage)) {
        update_post_meta($vehicle_id, 'mileage', $mileage);
    }

    // Add additional meta fields
    if (!empty($fuel_type)) {
        update_post_meta($vehicle_id, 'fuel_type', $fuel_type);
    }
    
    if (!empty($transmission)) {
        update_post_meta($vehicle_id, 'transmission', $transmission);
    }

    // Set default values for other expected fields
    update_post_meta($vehicle_id, 'condition', 'Good');
    update_post_meta($vehicle_id, 'purchase_date', '');
    update_post_meta($vehicle_id, 'last_service', '');
    update_post_meta($vehicle_id, 'next_service', '');

    wp_send_json_success(array(
        'message' => "Vehicle '$vehicle_name' ($year $make $model) added successfully! You can now add photos and additional details using the full editor.",
        'vehicle_id' => $vehicle_id,
        'redirect_url' => get_permalink($vehicle_id),
        'vehicle_data' => array(
            'name' => $vehicle_name,
            'make' => $make,
            'model' => $model,
            'year' => $year
        )
    ));
}

add_action('wp_ajax_add_vehicle', 'autohub_handle_add_vehicle');
add_action('wp_ajax_nopriv_add_vehicle', 'autohub_handle_add_vehicle');

/**
 * Send 2FA disabled notification email
 */
function autohub_send_2fa_disabled_notification($user_id) {
    $user = get_user_by('ID', $user_id);
    if (!$user) {
        return false;
    }
    
    $user_name = $user->display_name;
    $user_email = $user->user_email;
    $disabled_time = current_time('F j, Y \a\t g:i A');
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $dashboard_url = autohub_get_dashboard_url();
    
    $subject = 'Security Alert: Two-Factor Authentication Disabled - AutoHub Zambia';
    
    $message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>2FA Disabled - Security Alert</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f8f9fa;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #800080 0%, #008000 100%); padding: 30px 20px; text-align: center;">
                <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">AutoHub Zambia</h1>
                <p style="color: #ffffff; margin: 10px 0 0 0; opacity: 0.9;">Security Alert</p>
            </div>
            
            <!-- Content -->
            <div style="padding: 40px 30px;">
                <!-- Alert Icon -->
                <div style="text-align: center; margin-bottom: 30px;">
                    <div style="display: inline-block; width: 80px; height: 80px; background-color: #fef2f2; border-radius: 50%; position: relative;">
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #dc2626; font-size: 40px;">⚠️</div>
                    </div>
                </div>
                
                <h2 style="color: #1f2937; margin: 0 0 20px 0; font-size: 24px; text-align: center;">Two-Factor Authentication Disabled</h2>
                
                <p style="color: #4b5563; line-height: 1.6; margin: 0 0 20px 0;">
                    Hello ' . esc_html($user_name) . ',
                </p>
                
                <p style="color: #4b5563; line-height: 1.6; margin: 0 0 20px 0;">
                    This is a security notification to inform you that <strong>two-factor authentication (2FA) has been disabled</strong> for your AutoHub Zambia account.
                </p>
                
                <!-- Security Details -->
                <div style="background-color: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #dc2626; margin: 0 0 15px 0; font-size: 16px;">Security Details:</h3>
                    <ul style="color: #7f1d1d; margin: 0; padding-left: 20px; line-height: 1.6;">
                        <li><strong>Date & Time:</strong> ' . $disabled_time . '</li>
                        <li><strong>IP Address:</strong> ' . esc_html($user_ip) . '</li>
                        <li><strong>Account:</strong> ' . esc_html($user_email) . '</li>
                    </ul>
                </div>
                
                <!-- Warning -->
                <div style="background-color: #fffbeb; border: 1px solid #fed7aa; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #d97706; margin: 0 0 15px 0; font-size: 16px;">⚠️ Important Security Notice:</h3>
                    <p style="color: #92400e; margin: 0; line-height: 1.6;">
                        Your account is now <strong>less secure</strong> without 2FA protection. We strongly recommend re-enabling two-factor authentication as soon as possible to protect your account from unauthorized access.
                    </p>
                </div>
                
                <!-- Action Required -->
                <div style="text-align: center; margin: 30px 0;">
                    <p style="color: #4b5563; margin: 0 0 20px 0;">
                        If you did not disable 2FA, please secure your account immediately:
                    </p>
                    <a href="' . esc_url($dashboard_url) . '#settings" 
                       style="display: inline-block; background-color: #dc2626; color: #ffffff; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: bold; margin: 0 10px 10px 0;">
                        Secure My Account
                    </a>
                    <a href="' . esc_url($dashboard_url) . '#settings" 
                       style="display: inline-block; background-color: #059669; color: #ffffff; text-decoration: none; padding: 12px 30px; border-radius: 6px; font-weight: bold; margin: 0 10px 10px 0;">
                        Re-enable 2FA
                    </a>
                </div>
                
                <!-- Security Tips -->
                <div style="background-color: #f0f9ff; border: 1px solid #bae6fd; border-radius: 8px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: #0369a1; margin: 0 0 15px 0; font-size: 16px;">🛡️ Security Recommendations:</h3>
                    <ul style="color: #0c4a6e; margin: 0; padding-left: 20px; line-height: 1.6;">
                        <li>Change your password if you suspect unauthorized access</li>
                        <li>Re-enable two-factor authentication immediately</li>
                        <li>Review your recent account activity</li>
                        <li>Use a strong, unique password for your account</li>
                        <li>Never share your login credentials with anyone</li>
                    </ul>
                </div>
                
                <p style="color: #6b7280; line-height: 1.6; margin: 20px 0 0 0; font-size: 14px;">
                    If you have any questions or concerns about your account security, please contact our support team immediately.
                </p>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f9fafb; padding: 20px 30px; border-top: 1px solid #e5e7eb;">
                <p style="color: #6b7280; margin: 0; font-size: 14px; text-align: center;">
                    This is an automated security notification from AutoHub Zambia.<br>
                    Please do not reply to this email.
                </p>
                <p style="color: #9ca3af; margin: 10px 0 0 0; font-size: 12px; text-align: center;">
                    © ' . date('Y') . ' AutoHub Zambia. All rights reserved.
                </p>
            </div>
        </div>
    </body>
    </html>';
    
    $headers = array(
        'Content-Type: text/html; charset=UTF-8',
        'From: AutoHub Zambia Security <<EMAIL>>'
    );
    
    return wp_mail($user_email, $subject, $message, $headers);
}

/**
 * Debug function for 2FA (remove in production)
 */
function autohub_debug_2fa_status() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    if (isset($_GET['debug_2fa'])) {
        echo '<div style="background: #fff; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h3>2FA Debug Information</h3>';
        
        if (!session_id()) {
            session_start();
        }
        
        echo '<p><strong>Session ID:</strong> ' . session_id() . '</p>';
        echo '<p><strong>2FA Pending:</strong> ' . (isset($_SESSION['autohub_2fa_pending']) ? 'Yes' : 'No') . '</p>';
        echo '<p><strong>User ID in Session:</strong> ' . (isset($_SESSION['autohub_2fa_user_id']) ? $_SESSION['autohub_2fa_user_id'] : 'None') . '</p>';
        
        if (isset($_SESSION['autohub_2fa_user_id'])) {
            $user_id = $_SESSION['autohub_2fa_user_id'];
            $code = get_user_meta($user_id, '_autohub_2fa_code', true);
            $expiry = get_user_meta($user_id, '_autohub_2fa_code_expiry', true);
            $attempts = get_user_meta($user_id, '_autohub_2fa_attempts', true);
            
            echo '<p><strong>Stored Code:</strong> ' . $code . '</p>';
            echo '<p><strong>Code Expiry:</strong> ' . ($expiry ? date('Y-m-d H:i:s', $expiry) : 'None') . '</p>';
            echo '<p><strong>Current Time:</strong> ' . date('Y-m-d H:i:s') . '</p>';
            echo '<p><strong>Code Expired:</strong> ' . ($expiry && time() > $expiry ? 'Yes' : 'No') . '</p>';
            echo '<p><strong>Attempts:</strong> ' . $attempts . '</p>';
        }
        
        echo '</div>';
    }
}
add_action('wp_footer', 'autohub_debug_2fa_status');

/**
 * Fix existing accounts that were created before email verification system
 * This function can be called to manually fix accounts that are locked out
 */
function autohub_fix_existing_accounts() {
    // Only allow this to run for administrators
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Get all users
    $users = get_users();
    $fixed_count = 0;
    
    foreach ($users as $user) {
        $email_verified = get_user_meta($user->ID, 'email_verified', true);
        $registration_status = get_user_meta($user->ID, 'registration_status', true);
        
        // Skip if already has verification status
        if (!empty($email_verified)) {
            continue;
        }
        
        // For administrators, editors, authors - mark as verified
        if (in_array('administrator', $user->roles) || 
            in_array('editor', $user->roles) || 
            in_array('author', $user->roles)) {
            update_user_meta($user->ID, 'email_verified', 'yes');
            update_user_meta($user->ID, 'registration_status', 'approved');
            $fixed_count++;
        }
        
        // For consumers created before the system - mark as verified and approved
        elseif (in_array('consumer', $user->roles) && empty($registration_status)) {
            update_user_meta($user->ID, 'email_verified', 'yes');
            update_user_meta($user->ID, 'registration_status', 'approved');
            $fixed_count++;
        }
    }
    
    return $fixed_count;
}

/**
 * Admin notice to fix existing accounts
 */
function autohub_show_fix_accounts_notice() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Check if there are accounts that need fixing
    $users = get_users();
    $needs_fixing = false;
    
    foreach ($users as $user) {
        $email_verified = get_user_meta($user->ID, 'email_verified', true);
        if (empty($email_verified) && (in_array('administrator', $user->roles) || 
            in_array('editor', $user->roles) || 
            in_array('author', $user->roles) || 
            in_array('consumer', $user->roles))) {
            $needs_fixing = true;
            break;
        }
    }
    
    if ($needs_fixing && !get_option('autohub_accounts_fixed')) {
        ?>
        <div class="notice notice-warning is-dismissible">
            <p>
                <strong>AutoHub Email Verification:</strong> Some existing accounts need to be updated for the new email verification system. 
                <a href="<?php echo admin_url('admin.php?page=autohub-fix-accounts'); ?>" class="button button-primary">Fix Accounts Now</a>
            </p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'autohub_show_fix_accounts_notice');

/**
 * Add admin menu for fixing accounts
 */
function autohub_add_fix_accounts_menu() {
    add_management_page(
        'Fix AutoHub Accounts',
        'Fix AutoHub Accounts',
        'manage_options',
        'autohub-fix-accounts',
        'autohub_fix_accounts_page'
    );
}
add_action('admin_menu', 'autohub_add_fix_accounts_menu');

/**
 * Fix accounts admin page
 */
function autohub_fix_accounts_page() {
    if (isset($_POST['fix_accounts']) && wp_verify_nonce($_POST['_wpnonce'], 'fix_autohub_accounts')) {
        $fixed_count = autohub_fix_existing_accounts();
        update_option('autohub_accounts_fixed', true);
        echo '<div class="notice notice-success"><p>Fixed ' . $fixed_count . ' accounts successfully!</p></div>';
    }
    
    ?>
    <div class="wrap">
        <h1>Fix AutoHub Accounts</h1>
        <p>This tool will fix existing accounts that were created before the email verification system was implemented.</p>
        
        <div class="card">
            <h2>What this tool does:</h2>
            <ul>
                <li>✅ Marks all administrator, editor, and author accounts as email verified</li>
                <li>✅ Marks existing consumer accounts (created before verification system) as verified and approved</li>
                <li>✅ Allows locked-out accounts to log in again</li>
                <li>✅ Does not affect new consumer accounts that need proper verification</li>
            </ul>
        </div>
        
        <form method="post">
            <?php wp_nonce_field('fix_autohub_accounts'); ?>
            <p>
                <input type="submit" name="fix_accounts" class="button button-primary" value="Fix All Accounts" 
                       onclick="return confirm('Are you sure you want to fix all existing accounts? This action cannot be undone.');">
            </p>
        </form>
    </div>
    <?php
}

/**
 * Helper function to display vehicle compatibility information
 * 
 * @param int $listing_id The listing post ID
 * @param string $format Output format: 'list', 'table', 'inline'
 * @return string Formatted HTML output
 */
function autohub_get_vehicle_compatibility($listing_id, $format = 'list') {
    $compatibility = get_field('vehicle_compatibility', $listing_id);
    
    if (!$compatibility || empty($compatibility)) {
        return '<p class="text-gray-500 italic">No vehicle compatibility information available.</p>';
    }
    
    $output = '';
    
    switch ($format) {
        case 'table':
            $output .= '<div class="overflow-x-auto">';
            $output .= '<table class="min-w-full bg-white border border-gray-200 rounded-lg">';
            $output .= '<thead class="bg-gray-50">';
            $output .= '<tr>';
            $output .= '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Make</th>';
            $output .= '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Model</th>';
            $output .= '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Year Range</th>';
            $output .= '<th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Notes</th>';
            $output .= '</tr>';
            $output .= '</thead>';
            $output .= '<tbody class="divide-y divide-gray-200">';
            
            foreach ($compatibility as $vehicle) {
                $year_range = $vehicle['from_year'];
                if (!empty($vehicle['to_year'])) {
                    $year_range .= ' - ' . $vehicle['to_year'];
                } else {
                    $year_range .= ' - Present';
                }
                
                $output .= '<tr>';
                $output .= '<td class="px-4 py-2 text-sm font-medium text-gray-900">' . esc_html($vehicle['make']) . '</td>';
                $output .= '<td class="px-4 py-2 text-sm text-gray-700">' . esc_html($vehicle['model']) . '</td>';
                $output .= '<td class="px-4 py-2 text-sm text-gray-700">' . esc_html($year_range) . '</td>';
                $output .= '<td class="px-4 py-2 text-sm text-gray-500">' . esc_html($vehicle['notes'] ?? '') . '</td>';
                $output .= '</tr>';
            }
            
            $output .= '</tbody>';
            $output .= '</table>';
            $output .= '</div>';
            break;
            
        case 'inline':
            $vehicles = array();
            foreach ($compatibility as $vehicle) {
                $year_range = $vehicle['from_year'];
                if (!empty($vehicle['to_year'])) {
                    $year_range .= '-' . $vehicle['to_year'];
                } else {
                    $year_range .= '+';
                }
                $vehicles[] = $vehicle['make'] . ' ' . $vehicle['model'] . ' (' . $year_range . ')';
            }
            $output = '<span class="text-sm text-gray-700">' . implode(', ', $vehicles) . '</span>';
            break;
            
        case 'list':
        default:
            $output .= '<ul class="space-y-2">';
            foreach ($compatibility as $vehicle) {
                $year_range = $vehicle['from_year'];
                if (!empty($vehicle['to_year'])) {
                    $year_range .= ' - ' . $vehicle['to_year'];
                } else {
                    $year_range .= ' - Present';
                }
                
                $output .= '<li class="flex items-start space-x-3">';
                $output .= '<div class="flex-shrink-0 w-2 h-2 bg-primary rounded-full mt-2"></div>';
                $output .= '<div>';
                $output .= '<div class="font-medium text-gray-900">' . esc_html($vehicle['make']) . ' ' . esc_html($vehicle['model']) . '</div>';
                $output .= '<div class="text-sm text-gray-600">Years: ' . esc_html($year_range) . '</div>';
                if (!empty($vehicle['notes'])) {
                    $output .= '<div class="text-sm text-gray-500 mt-1">' . esc_html($vehicle['notes']) . '</div>';
                }
                $output .= '</div>';
                $output .= '</li>';
            }
            $output .= '</ul>';
            break;
    }
    
    return $output;
}

/**
 * Helper function to get vehicle compatibility as array
 * 
 * @param int $listing_id The listing post ID
 * @return array Array of vehicle compatibility data
 */
function autohub_get_vehicle_compatibility_data($listing_id) {
    $compatibility = get_field('vehicle_compatibility', $listing_id);
    
    if (!$compatibility || empty($compatibility)) {
        return array();
    }
    
    return $compatibility;
}

/**
 * Helper function to check if a part is compatible with a specific vehicle
 * 
 * @param int $listing_id The listing post ID
 * @param string $make Vehicle make
 * @param string $model Vehicle model (optional)
 * @param int $year Vehicle year (optional)
 * @return bool True if compatible, false otherwise
 */
function autohub_is_part_compatible($listing_id, $make, $model = '', $year = null) {
    $compatibility = autohub_get_vehicle_compatibility_data($listing_id);
    
    if (empty($compatibility)) {
        return false;
    }
    
    foreach ($compatibility as $vehicle) {
        // Check make (case insensitive)
        if (strcasecmp($vehicle['make'], $make) !== 0) {
            continue;
        }
        
        // Check model if provided (case insensitive)
        if (!empty($model) && strcasecmp($vehicle['model'], $model) !== 0) {
            continue;
        }
        
        // Check year if provided
        if ($year !== null) {
            $from_year = intval($vehicle['from_year']);
            $to_year = !empty($vehicle['to_year']) ? intval($vehicle['to_year']) : date('Y');
            
            if ($year < $from_year || $year > $to_year) {
                continue;
            }
        }
        
        // If we get here, it's compatible
        return true;
    }
    
    return false;
}

/**
 * Helper function to format operating hours display
 * 
 * @param array $operating_hours Array of operating hours data
 * @param bool $show_html Whether to return HTML formatted output
 * @return string Formatted operating hours string
 */
function autohub_format_operating_hours($operating_hours, $show_html = true) {
    if (!$operating_hours || !is_array($operating_hours)) {
        return $show_html ? '<span class="text-gray-500 text-sm">Contact us for operating hours</span>' : 'Contact us for operating hours';
    }
    
    $formatted_hours = array();
    
    foreach ($operating_hours as $hours) {
        // Handle both old and new data structures
        if (isset($hours['from_day'])) {
            // New structure
            $day_display = '';
            if (!empty($hours['to_day']) && $hours['from_day'] !== $hours['to_day']) {
                $day_display = $hours['from_day'] . ' – ' . $hours['to_day'];
            } else {
                $day_display = $hours['from_day'];
            }
            
            $time_display = '';
            if (!empty($hours['closed_all_day'])) {
                $time_display = $show_html ? '<span class="text-red-600 font-medium">Closed</span>' : 'Closed';
            } elseif (isset($hours['open_time']) && isset($hours['close_time'])) {
                $open_time = autohub_convert_to_24hour($hours['open_time']);
                $close_time = autohub_convert_to_24hour($hours['close_time']);
                $time_display = $open_time . ' – ' . $close_time;
            } else {
                $time_display = $show_html ? '<span class="text-gray-400">Contact for hours</span>' : 'Contact for hours';
            }
        } elseif (isset($hours['day'])) {
            // Old structure - backward compatibility
            $day_display = $hours['day'];
            $time_display = '';
            if (isset($hours['open_time']) && isset($hours['close_time'])) {
                $open_time = autohub_convert_to_24hour($hours['open_time']);
                $close_time = autohub_convert_to_24hour($hours['close_time']);
                $time_display = $open_time . ' – ' . $close_time;
            } else {
                $time_display = $show_html ? '<span class="text-gray-400">Contact for hours</span>' : 'Contact for hours';
            }
        } else {
            continue; // Skip invalid entries
        }
        
        if ($show_html) {
            $formatted_hours[] = '<div class="flex justify-between items-center">
                <span class="text-gray-700 font-medium">' . esc_html($day_display) . '</span>
                <span class="text-gray-600 text-sm">' . $time_display . '</span>
            </div>';
        } else {
            $formatted_hours[] = $day_display . ': ' . strip_tags($time_display);
        }
    }
    
    if (empty($formatted_hours)) {
        return $show_html ? '<span class="text-gray-500 text-sm">Contact us for operating hours</span>' : 'Contact us for operating hours';
    }
    
    return $show_html ? implode('', $formatted_hours) : implode("\n", $formatted_hours);
}

/**
 * Helper function to get current operating status
 * 
 * @param array $operating_hours Array of operating hours data
 * @return array Array with 'is_open' boolean and 'message' string
 */
function autohub_get_current_operating_status($operating_hours) {
    if (!$operating_hours || !is_array($operating_hours)) {
        return array(
            'is_open' => null,
            'message' => 'Contact for hours'
        );
    }
    
    // Set timezone to Zambia (Central Africa Time - CAT, UTC+2)
    $zambia_timezone = new DateTimeZone('Africa/Lusaka');
    $current_datetime = new DateTime('now', $zambia_timezone);
    
    $current_day = $current_datetime->format('l'); // Full day name (e.g., 'Monday')
    $current_time = $current_datetime->format('H:i'); // 24-hour format time
    
    $day_order = array(
        'Monday' => 1, 'Tuesday' => 2, 'Wednesday' => 3, 'Thursday' => 4,
        'Friday' => 5, 'Saturday' => 6, 'Sunday' => 7
    );
    
    foreach ($operating_hours as $hours) {
        // Handle both old and new data structures
        $from_day = '';
        $to_day = '';
        
        if (isset($hours['from_day'])) {
            // New structure
            $from_day = $hours['from_day'];
            $to_day = $hours['to_day'] ?? '';
        } elseif (isset($hours['day'])) {
            // Old structure - backward compatibility
            $from_day = $hours['day'];
            $to_day = '';
        } else {
            continue;
        }
        
        $from_day_num = $day_order[$from_day] ?? 0;
        $to_day_num = !empty($to_day) ? ($day_order[$to_day] ?? $from_day_num) : $from_day_num;
        $current_day_num = $day_order[$current_day] ?? 0;
        
        // Check if current day falls within the range
        $day_matches = false;
        if ($from_day_num <= $to_day_num) {
            // Normal range (e.g., Monday to Friday)
            $day_matches = ($current_day_num >= $from_day_num && $current_day_num <= $to_day_num);
        } else {
            // Wrap-around range (e.g., Saturday to Monday)
            $day_matches = ($current_day_num >= $from_day_num || $current_day_num <= $to_day_num);
        }
        
        if ($day_matches) {
            if (!empty($hours['closed_all_day'])) {
                return array(
                    'is_open' => false,
                    'message' => 'Closed today'
                );
            }
            
            if (isset($hours['open_time']) && isset($hours['close_time'])) {
                $open_time = autohub_convert_to_24hour($hours['open_time']);
                $close_time = autohub_convert_to_24hour($hours['close_time']);
                
                if ($current_time >= $open_time && $current_time <= $close_time) {
                    return array(
                        'is_open' => true,
                        'message' => 'Open now (closes at ' . $close_time . ')'
                    );
                } else {
                    return array(
                        'is_open' => false,
                        'message' => 'Closed (opens at ' . $open_time . ')'
                    );
                }
            }
        }
    }
    
    return array(
        'is_open' => null,
        'message' => 'Contact for hours'
    );
}

/**
 * Helper function to convert time to 24-hour format
 * 
 * @param string $time Time string in various formats
 * @return string Time in 24-hour format (H:i)
 */
function autohub_convert_to_24hour($time) {
    if (empty($time)) {
        return '';
    }
    
    // If already in 24-hour format (no am/pm), return as is
    if (!preg_match('/(am|pm)/i', $time)) {
        // Ensure it's in H:i format
        $parsed = date_parse($time);
        if ($parsed['hour'] !== false && $parsed['minute'] !== false) {
            return sprintf('%02d:%02d', $parsed['hour'], $parsed['minute']);
        }
        return $time;
    }
    
    // Convert 12-hour format to 24-hour format
    try {
        $datetime = DateTime::createFromFormat('g:i a', $time);
        if (!$datetime) {
            $datetime = DateTime::createFromFormat('h:i a', $time);
        }
        if (!$datetime) {
            $datetime = DateTime::createFromFormat('g:i A', $time);
        }
        if (!$datetime) {
            $datetime = DateTime::createFromFormat('h:i A', $time);
        }
        
        if ($datetime) {
            return $datetime->format('H:i');
        }
    } catch (Exception $e) {
        // If conversion fails, return original
    }
    
    return $time;
}

/**
 * Clear ACF field cache to resolve field conflicts
 * Call this function after updating field definitions
 */
function autohub_clear_acf_cache() {
    if (function_exists('acf_get_store')) {
        // Clear ACF field cache
        acf_get_store('fields')->reset();
        acf_get_store('field-groups')->reset();
    }
    
    // Clear WordPress object cache
    wp_cache_flush();
    
    // Clear any transients related to ACF
    delete_transient('acf_field_groups');
    delete_transient('acf_fields');
}

// Clear ACF cache on theme activation or when fields are updated
add_action('after_switch_theme', 'autohub_clear_acf_cache');
add_action('acf/save_post', 'autohub_clear_acf_cache', 20);

