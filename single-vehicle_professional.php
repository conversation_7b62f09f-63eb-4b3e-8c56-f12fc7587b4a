<?php
/**
 * The template for displaying single vehicle professional
 *
 * @package AutoHub_Zambia
 */

// Helper function to get custom fields safely
function get_professional_field($field_name, $post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    // Try SCF first if available
    if (class_exists('SCF')) {
        return SCF::get($field_name, $post_id);
    }
    
    // Fallback to get_post_meta
    return get_post_meta($post_id, $field_name, true);
}

get_header(); ?>

<main class="container mx-auto px-4 py-8">
    <?php while (have_posts()) : the_post(); ?>
        <div class="max-w-6xl mx-auto">
            <!-- Header Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <div class="flex flex-col md:flex-row md:items-start md:space-x-6">
                    <!-- Profile Photo -->
                    <?php
                    $profile_image = get_professional_field('profile_image');
                    if ($profile_image || has_post_thumbnail()) : ?>
                        <div class="flex-shrink-0 mb-4 md:mb-0">
                            <div class="w-32 h-32 rounded-lg overflow-hidden">
                                <?php if ($profile_image) : ?>
                                    <img src="<?php echo esc_url(wp_get_attachment_image_url($profile_image, 'medium')); ?>"
                                         alt="<?php the_title(); ?>"
                                         class="w-full h-full object-cover">
                                <?php else : ?>
                                    <?php the_post_thumbnail('medium', array('class' => 'w-full h-full object-cover')); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Professional Info -->
                    <div class="flex-grow">
                        <h1 class="text-3xl font-serif font-bold text-gray-900 mb-2">
                            <?php 
                            $full_name = get_professional_field('full_name');
                            echo $full_name ? esc_html($full_name) : get_the_title(); 
                            ?>
                        </h1>
                        
                        <?php 
                        $professional_type = get_professional_field('professional_type');
                        if ($professional_type) : ?>
                            <div class="mb-4">
                                <span class="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                                    <?php echo $professional_type === 'body_specialist' ? 'Body Specialist' : 'Mechanic'; ?>
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Three Cards Section -->
            <div class="grid md:grid-cols-3 gap-6 mb-8">
                <!-- Card 1: Physical Address -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-serif font-bold text-gray-900 mb-4">Physical Address</h3>
                    
                    <?php
                    $address = get_professional_field('address');
                    $province = get_professional_field('province');
                    $city_town = get_professional_field('city_town');

                    // Handle relationship fields - they return post IDs
                    $province_name = '';
                    $city_name = '';

                    if ($province) {
                        if (is_array($province) && !empty($province)) {
                            $province_id = reset($province);
                        } else {
                            $province_id = $province;
                        }
                        if ($province_id && is_numeric($province_id)) {
                            $province_post = get_post($province_id);
                            if ($province_post) {
                                $province_name = $province_post->post_title;
                            }
                        }
                    }

                    if ($city_town) {
                        if (is_array($city_town) && !empty($city_town)) {
                            $city_id = reset($city_town);
                        } else {
                            $city_id = $city_town;
                        }
                        if ($city_id && is_numeric($city_id)) {
                            $city_post = get_post($city_id);
                            if ($city_post) {
                                $city_name = $city_post->post_title;
                            }
                        }
                    }
                    ?>
                    
                    <div class="space-y-3">
                        <?php if ($address) : ?>
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <div class="text-gray-700">
                                    <?php echo esc_html($address); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($city_name || $province_name) : ?>
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5L15 7h4a2 2 0 012 2v10a2 2 0 01-2 2h-14a2 2 0 01-2-2z"></path>
                                </svg>
                                <div class="text-gray-700">
                                    <?php
                                    if ($city_name && $province_name) {
                                        echo esc_html($city_name) . ', ' . esc_html($province_name);
                                    } elseif ($city_name) {
                                        echo esc_html($city_name);
                                    } elseif ($province_name) {
                                        echo esc_html($province_name);
                                    }
                                    ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Card 2: Contact Details -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-serif font-bold text-gray-900 mb-4">Contact Details</h3>
                    
                    <?php 
                    $phone_number = get_professional_field('phone_number');
                    $email_address = get_professional_field('email_address');
                    $website = get_professional_field('website');
                    ?>
                    
                    <div class="space-y-3">
                        <?php if ($phone_number) : ?>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <a href="tel:<?php echo esc_attr($phone_number); ?>" class="text-gray-700 hover:text-primary">
                                    <?php echo esc_html($phone_number); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($email_address) : ?>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <a href="mailto:<?php echo esc_attr($email_address); ?>" class="text-gray-700 hover:text-primary">
                                    <?php echo esc_html($email_address); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($website) : ?>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                                </svg>
                                <a href="<?php echo esc_url($website); ?>" target="_blank" rel="noopener noreferrer" class="text-gray-700 hover:text-primary">
                                    <?php echo esc_url($website); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Card 3: Operating Hours -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-serif font-bold text-gray-900 mb-4">Operating Hours</h3>
                    
                    <?php 
                    $operating_hours = get_professional_field('operating_hours');
                    if ($operating_hours && is_array($operating_hours)) : ?>
                        <div class="space-y-2">
                            <?php foreach ($operating_hours as $hours) : ?>
                                <div class="flex justify-between items-center py-1 border-b border-gray-100">
                                    <span class="text-gray-700 font-medium">
                                        <?php 
                                        if (isset($hours['from_day'])) {
                                            echo esc_html($hours['from_day']);
                                            if (!empty($hours['to_day'])) {
                                                echo ' - ' . esc_html($hours['to_day']);
                                            }
                                        }
                                        ?>
                                    </span>
                                    <span class="text-gray-600">
                                        <?php 
                                        if (isset($hours['closed_all_day']) && $hours['closed_all_day']) {
                                            echo '<span class="text-red-500">Closed</span>';
                                        } else {
                                            if (isset($hours['open_time']) && isset($hours['close_time'])) {
                                                echo esc_html($hours['open_time']) . ' - ' . esc_html($hours['close_time']);
                                            } else {
                                                echo 'Hours not specified';
                                            }
                                        }
                                        ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else : ?>
                        <p class="text-gray-500 italic">No operating hours specified</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Tabs Section -->
            <div class="bg-white rounded-lg shadow-md mb-8" x-data="{ activeTab: 'profile' }">
                <!-- Tabs Navigation -->
                <div class="border-b border-gray-200">
                    <nav class="flex -mb-px">
                        <button @click="activeTab = 'profile'" 
                                :class="{ 'border-primary text-primary': activeTab === 'profile', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'profile' }"
                                class="py-4 px-6 border-b-2 font-medium text-sm sm:text-base focus:outline-none transition-colors duration-200">
                            My Profile
                        </button>
                        <button @click="activeTab = 'services'" 
                                :class="{ 'border-primary text-primary': activeTab === 'services', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'services' }"
                                class="py-4 px-6 border-b-2 font-medium text-sm sm:text-base focus:outline-none transition-colors duration-200">
                            Services Offered
                        </button>
                        <button @click="activeTab = 'gallery'" 
                                :class="{ 'border-primary text-primary': activeTab === 'gallery', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'gallery' }"
                                class="py-4 px-6 border-b-2 font-medium text-sm sm:text-base focus:outline-none transition-colors duration-200">
                            Gallery
                        </button>
                    </nav>
                </div>
                
                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Profile Tab -->
                    <div x-show="activeTab === 'profile'" class="space-y-6">
                        <?php 
                        $short_bio = get_professional_field('short_bio');
                        $experience_years = get_professional_field('experience_years');
                        $service_rate = get_professional_field('service_rate');
                        $certifications = get_professional_field('certifications');
                        $specializations = get_professional_field('specializations');
                        ?>
                        
                        <!-- Bio Section -->
                        <?php if ($short_bio || get_the_content()) : ?>
                            <div>
                                <h3 class="text-xl font-serif font-bold text-gray-900 mb-3">About</h3>
                                <?php if ($short_bio) : ?>
                                    <p class="text-gray-700 leading-relaxed mb-4">
                                        <?php echo esc_html($short_bio); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <?php if (get_the_content()) : ?>
                                    <div class="prose prose-lg max-w-none">
                                        <?php the_content(); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Experience & Rate -->
                        <?php if ($experience_years || $service_rate) : ?>
                            <div>
                                <h3 class="text-xl font-serif font-bold text-gray-900 mb-3">Experience & Rates</h3>
                                <div class="grid sm:grid-cols-2 gap-4">
                                    <?php if ($experience_years) : ?>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <span class="block text-sm text-gray-500 mb-1">Experience</span>
                                            <span class="text-lg font-medium text-gray-800"><?php echo esc_html($experience_years); ?> years</span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($service_rate) : ?>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <span class="block text-sm text-gray-500 mb-1">Service Rate</span>
                                            <span class="text-lg font-medium text-gray-800">ZMW <?php echo esc_html($service_rate); ?>/hour</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Specializations -->
                        <?php if ($specializations && is_array($specializations)) : ?>
                            <div>
                                <h3 class="text-xl font-serif font-bold text-gray-900 mb-3">Specializations</h3>
                                <div class="grid md:grid-cols-2 gap-3">
                                    <?php foreach ($specializations as $spec) : ?>
                                        <?php if (isset($spec['specialization_item']) && !empty($spec['specialization_item'])) : ?>
                                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                <svg class="w-5 h-5 text-secondary mr-3" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                </svg>
                                                <span class="text-gray-700 font-medium"><?php echo esc_html($spec['specialization_item']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Certifications -->
                        <?php if ($certifications) : ?>
                            <div>
                                <h3 class="text-xl font-serif font-bold text-gray-900 mb-3">Certifications & Qualifications</h3>
                                <div class="prose prose-lg max-w-none">
                                    <?php echo wp_kses_post(nl2br($certifications)); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Services Tab -->
                    <div x-show="activeTab === 'services'" class="space-y-6">
                        <?php
                        // Try multiple methods to get services
                        $services_offered = get_professional_field('services_offered');

                        // If we get a string (likely count), try to get individual items
                        if (is_string($services_offered) && is_numeric($services_offered)) {
                            $services_count = intval($services_offered);
                            $services_offered = array();

                            // Try to get individual service items
                            for ($i = 0; $i < $services_count; $i++) {
                                // Try different SCF patterns for repeater fields
                                $service_name = get_professional_field("services_offered_{$i}_service_name");
                                if (empty($service_name)) {
                                    $service_name = get_professional_field("services_offered_{$i}_service_item");
                                }
                                if (empty($service_name)) {
                                    // Try direct meta approach
                                    $service_name = get_post_meta(get_the_ID(), "services_offered_{$i}_service_name", true);
                                }
                                if (empty($service_name)) {
                                    $service_name = get_post_meta(get_the_ID(), "services_offered_{$i}_service_item", true);
                                }

                                if (!empty($service_name)) {
                                    $services_offered[] = array('service_name' => $service_name);
                                }
                            }
                        }

                        // Also try direct get_post_meta as fallback
                        if (empty($services_offered) || !is_array($services_offered)) {
                            $services_offered = get_post_meta(get_the_ID(), 'services_offered', true);
                        }



                        if ($services_offered && is_array($services_offered) && !empty($services_offered)) : ?>
                            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <?php foreach ($services_offered as $service) : ?>
                                    <?php
                                    // Handle both possible sub-field names
                                    $service_text = '';
                                    if (isset($service['service_name']) && !empty($service['service_name'])) {
                                        $service_text = $service['service_name'];
                                    } elseif (isset($service['service_item']) && !empty($service['service_item'])) {
                                        $service_text = $service['service_item'];
                                    }

                                    if (!empty($service_text)) : ?>
                                        <div class="bg-gray-50 p-4 rounded-lg">
                                            <div class="flex items-center">
                                                <svg class="w-5 h-5 text-secondary mr-3" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                                </svg>
                                                <span class="text-gray-700 font-medium"><?php echo esc_html($service_text); ?></span>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        <?php else : ?>
                            <p class="text-gray-500 italic">No services specified</p>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Gallery Tab -->
                    <div x-show="activeTab === 'gallery'" class="space-y-6">
                        <?php 
                        $gallery = get_professional_field('gallery');
                        if ($gallery && !empty($gallery)) : ?>
                            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <?php 
                                // Handle different return formats for gallery field
                                if (is_string($gallery)) {
                                    // If it's a comma-separated string of IDs
                                    $gallery_ids = explode(',', $gallery);
                                } elseif (is_array($gallery)) {
                                    $gallery_ids = $gallery;
                                } else {
                                    $gallery_ids = array();
                                }
                                
                                foreach ($gallery_ids as $image_id) : 
                                    if (empty($image_id)) continue;
                                ?>
                                    <div class="group relative overflow-hidden rounded-lg">
                                        <img src="<?php echo esc_url(wp_get_attachment_image_url($image_id, 'medium')); ?>" 
                                             alt="Gallery image" 
                                             class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else : ?>
                            <p class="text-gray-500 italic">No gallery images available</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endwhile; ?>
</main>

<?php get_footer(); ?>