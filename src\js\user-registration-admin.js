/**
 * AutoHub User Registration Admin Scripts
 * Handles admin functionality for pending users management
 */

(function($) {
    'use strict';

    $(document).ready(function() {


        // Handle auto-approve toggle
        $('#auto-approve-toggle').on('change', function() {
            const isChecked = $(this).is(':checked');
            
            $.ajax({
                url: autohub_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'autohub_toggle_auto_approve',
                    auto_approve: isChecked ? 'yes' : 'no',
                    nonce: autohub_admin_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Auto-approve setting updated successfully
                    } else {
                        console.error('AutoHub Admin: Failed to update auto-approve setting');
                        // Revert checkbox state
                        $('#auto-approve-toggle').prop('checked', !isChecked);
                    }
                },
                error: function() {
                    console.error('AutoHub Admin: Error updating auto-approve setting');
                    // Revert checkbox state
                    $('#auto-approve-toggle').prop('checked', !isChecked);
                }
            });
        });

        // Handle user approval
        $('.approve-user').on('click', function() {
            const userId = $(this).data('user-id');
            const $button = $(this);
            const $row = $button.closest('tr');
            
            $button.prop('disabled', true).text('Approving...');
            
            $.ajax({
                url: autohub_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'autohub_approve_user',
                    user_id: userId,
                    nonce: autohub_admin_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $row.fadeOut(500, function() {
                            $(this).remove();
                        });
                        
                        // Show success message
                        showAdminMessage('User approved successfully!', 'success');
                    } else {
                        showAdminMessage(response.data.message || 'Failed to approve user', 'error');
                        $button.prop('disabled', false).text('Approve');
                    }
                },
                error: function() {
                    showAdminMessage('Error approving user', 'error');
                    $button.prop('disabled', false).text('Approve');
                }
            });
        });

        // Handle user rejection
        $('.reject-user').on('click', function() {
            const userId = $(this).data('user-id');
            const $button = $(this);
            const $row = $button.closest('tr');
            
            if (!confirm('Are you sure you want to reject this user? This action cannot be undone.')) {
                return;
            }
            
            $button.prop('disabled', true).text('Rejecting...');
            
            $.ajax({
                url: autohub_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'autohub_reject_user',
                    user_id: userId,
                    nonce: autohub_admin_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $row.fadeOut(500, function() {
                            $(this).remove();
                        });
                        
                        // Show success message
                        showAdminMessage('User rejected successfully!', 'success');
                    } else {
                        showAdminMessage(response.data.message || 'Failed to reject user', 'error');
                        $button.prop('disabled', false).text('Reject');
                    }
                },
                error: function() {
                    showAdminMessage('Error rejecting user', 'error');
                    $button.prop('disabled', false).text('Reject');
                }
            });
        });

        // Handle resend verification
        $('.resend-verification').on('click', function() {
            const userId = $(this).data('user-id');
            const userEmail = $(this).data('user-email');
            const $button = $(this);
            const $statusCell = $button.closest('tr').find('td:nth-child(7)'); // Status column
            
            $button.prop('disabled', true).text('Sending...');
            
            $.ajax({
                url: autohub_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'autohub_resend_verification',
                    email: userEmail,
                    nonce: autohub_admin_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showAdminMessage(response.data.message, 'success');
                        $button.text('Email Sent!');
                        
                        // Update status cell to show new attempt info
                        const currentTime = new Date();
                        const timeString = 'just now';
                        $statusCell.html('<span style="color: #dba617;">✉ Email Not Verified</span><br><small>Last sent: ' + timeString + '</small>');
                        
                        // Re-enable button after 30 seconds
                        setTimeout(function() {
                            $button.prop('disabled', false).text('Resend Verification');
                        }, 30000);
                    } else {
                        showAdminMessage(response.data.message || 'Failed to send verification email', 'error');
                        $button.prop('disabled', false).text('Resend Verification');
                    }
                },
                error: function() {
                    showAdminMessage('Error sending verification email', 'error');
                    $button.prop('disabled', false).text('Resend Verification');
                }
            });
        });

        // Bulk actions functionality
        $('#bulk-action-selector-top, #bulk-action-selector-bottom').on('change', function() {
            const action = $(this).val();
            const $checkboxes = $('.user-checkbox:checked');
            
            if (action === 'resend-verification' && $checkboxes.length > 0) {
                if (confirm('Send verification emails to ' + $checkboxes.length + ' selected users?')) {
                    processBulkResendVerification($checkboxes);
                }
            }
        });

        // Process bulk resend verification
        function processBulkResendVerification($checkboxes) {
            let processed = 0;
            const total = $checkboxes.length;
            
            $checkboxes.each(function() {
                const userId = $(this).val();
                const userEmail = $(this).data('user-email');
                const $row = $(this).closest('tr');
                
                $.ajax({
                    url: autohub_admin_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'autohub_resend_verification',
                        email: userEmail,
                        nonce: autohub_admin_ajax.nonce
                    },
                    success: function(response) {
                        processed++;
                        if (response.success) {
                            $row.find('.resend-verification').text('Email Sent!');
                        }
                        
                        if (processed === total) {
                            showAdminMessage('Bulk verification emails processed!', 'success');
                        }
                    },
                    error: function() {
                        processed++;
                        if (processed === total) {
                            showAdminMessage('Bulk verification emails completed with some errors', 'warning');
                        }
                    }
                });
            });
        }

        // Show admin message
        function showAdminMessage(message, type) {
            const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut();
            }, 5000);
        }
    });

})(jQuery);