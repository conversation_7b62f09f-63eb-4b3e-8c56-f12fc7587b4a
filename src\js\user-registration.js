/**
 * AutoHub User Registration Live Validation
 * Provides real-time form validation for registration forms
 */

(function($) {
    'use strict';

    // Validation patterns
    const patterns = {
        username: /^[a-zA-Z0-9_-]+$/,
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        phone: /^(\+260|0)[0-9]{9}$/,
        password: {
            minLength: 8,
            hasLowercase: /[a-z]/,
            hasUppercase: /[A-Z]/,
            hasNumber: /[0-9]/,
            hasSpecial: /[!@#$%^&*(),.?":{}|<>]/
        }
    };

    // Validation messages
    const messages = {
        username: {
            required: 'Username is required',
            minLength: 'Username must be at least 3 characters long',
            pattern: 'Username can only contain letters, numbers, underscores, and hyphens',
            exists: 'This username is already taken'
        },
        email: {
            required: 'Email address is required',
            invalid: 'Please enter a valid email address',
            exists: 'This email address is already registered'
        },
        phone: {
            required: 'Phone number is required',
            invalid: 'Please enter a valid Zambian phone number (e.g., +260 XXX XXX XXX)'
        },
        password: {
            required: 'Password is required',
            minLength: 'Password must be at least 8 characters long',
            weak: 'Password should contain uppercase, lowercase, number, and special character'
        },
        confirmPassword: {
            required: 'Please confirm your password',
            mismatch: 'Passwords do not match'
        },
        businessName: {
            required: 'Business name is required',
            minLength: 'Business name must be at least 2 characters long'
        },
        firstName: {
            required: 'First name is required',
            minLength: 'First name must be at least 2 characters long'
        },
        lastName: {
            required: 'Last name is required',
            minLength: 'Last name must be at least 2 characters long'
        },
        corporateName: {
            required: 'Corporate name is required',
            minLength: 'Corporate name must be at least 2 characters long'
        }
    };

    // Debounce function for API calls
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Show field feedback
    function showFieldFeedback(field, message, type = 'error') {
        const $field = $(field);
        // Try to find feedback element as sibling first, then in parent container
        let $feedback = $field.siblings('.field-feedback');
        if ($feedback.length === 0) {
            $feedback = $field.closest('.form-group').find('.field-feedback');
        }
        
        // Remove existing classes
        $field.removeClass('border-red-500 border-green-500 border-gray-300');
        $feedback.removeClass('text-red-500 text-green-500 hidden');
        
        if (type === 'error') {
            $field.addClass('border-red-500');
            $feedback.addClass('text-red-500').text(message).removeClass('hidden');
        } else if (type === 'success') {
            $field.addClass('border-green-500');
            $feedback.addClass('text-green-500').text(message).removeClass('hidden');
        } else {
            $field.addClass('border-gray-300');
            $feedback.addClass('hidden');
        }
    }

    // Clear field feedback
    function clearFieldFeedback(field) {
        const $field = $(field);
        const $feedback = $field.siblings('.field-feedback');
        
        $field.removeClass('border-red-500 border-green-500').addClass('border-gray-300');
        $feedback.addClass('hidden');
    }

    // Validate username
    function validateUsername(username, callback) {
        if (!username) {
            callback(messages.username.required, 'error');
            return;
        }

        if (username.length < 3) {
            callback(messages.username.minLength, 'error');
            return;
        }

        if (!patterns.username.test(username)) {
            callback(messages.username.pattern, 'error');
            return;
        }

        // Check if username exists
        $.ajax({
            url: autohub_reg_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'autohub_check_username',
                username: username,
                nonce: autohub_reg_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.available) {
                        callback('Username is available', 'success');
                    } else {
                        callback(messages.username.exists, 'error');
                    }
                } else {
                    callback(response.data.message || 'Error checking username', 'error');
                }
            },
            error: function() {
                callback('Error checking username', 'error');
            }
        });
    }

    // Validate email
    function validateEmail(email, callback) {
        if (!email) {
            callback(messages.email.required, 'error');
            return;
        }

        if (!patterns.email.test(email)) {
            callback(messages.email.invalid, 'error');
            return;
        }

        // Check if email exists
        $.ajax({
            url: autohub_reg_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'autohub_check_email',
                email: email,
                nonce: autohub_reg_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.available) {
                        callback('Email is available', 'success');
                    } else {
                        callback(messages.email.exists, 'error');
                    }
                } else {
                    callback(response.data.message || 'Error checking email', 'error');
                }
            },
            error: function() {
                callback('Error checking email', 'error');
            }
        });
    }

    // Validate phone number
    function validatePhone(phone) {
        if (!phone) {
            return { valid: false, message: messages.phone.required };
        }

        // Clean phone number (remove spaces and dashes)
        const cleanPhone = phone.replace(/[\s-]/g, '');
        
        if (!patterns.phone.test(cleanPhone)) {
            return { valid: false, message: messages.phone.invalid };
        }

        return { valid: true, message: 'Valid phone number' };
    }

    // Validate password
    function validatePassword(password) {
        if (!password) {
            return { valid: false, message: messages.password.required, strength: 'none' };
        }

        if (password.length < patterns.password.minLength) {
            return { valid: false, message: messages.password.minLength, strength: 'weak' };
        }

        let strength = 'weak';
        let score = 0;
        let missing = [];

        // Check each requirement
        if (patterns.password.hasLowercase.test(password)) {
            score++;
        } else {
            missing.push('lowercase letter');
        }

        if (patterns.password.hasUppercase.test(password)) {
            score++;
        } else {
            missing.push('uppercase letter');
        }

        if (patterns.password.hasNumber.test(password)) {
            score++;
        } else {
            missing.push('number');
        }

        if (patterns.password.hasSpecial.test(password)) {
            score++;
        } else {
            missing.push('special character');
        }

        // Determine strength
        if (score >= 4) strength = 'strong';
        else if (score >= 3) strength = 'medium';

        // Require at least medium strength (3 out of 4 criteria)
        const valid = score >= 3;
        
        let message;
        if (valid) {
            message = `Password strength: ${strength}`;
        } else {
            if (missing.length === 1) {
                message = `Password needs: ${missing[0]}`;
            } else if (missing.length === 2) {
                message = `Password needs: ${missing.join(' and ')}`;
            } else {
                message = `Password needs: ${missing.slice(0, -1).join(', ')} and ${missing[missing.length - 1]}`;
            }
        }

        return { valid, message, strength };
    }

    // Validate confirm password
    function validateConfirmPassword(password, confirmPassword) {
        if (!confirmPassword) {
            return { valid: false, message: messages.confirmPassword.required };
        }

        if (password !== confirmPassword) {
            return { valid: false, message: messages.confirmPassword.mismatch };
        }

        return { valid: true, message: 'Passwords match' };
    }

    // Validate required text field
    function validateRequiredText(value, fieldName, minLength = 2) {
        // Check if fieldName exists in messages object
        if (!messages[fieldName]) {
            return { valid: false, message: 'This field is required' };
        }

        if (!value || value.trim() === '') {
            return { valid: false, message: messages[fieldName].required };
        }

        if (value.trim().length < minLength) {
            return { valid: false, message: messages[fieldName].minLength };
        }

        return { valid: true, message: '' };
    }

    // Initialize consumer registration form with live validation
    function initConsumerRegistration() {
        let $form = $('#autohub-registration-form');
        
        // Fallback: try to find form by other means
        if (!$form.length) {
            $form = $('form').filter(function() {
                return $(this).find('input[name="consumer_type"]').length > 0;
            });
        }
        
        if (!$form.length) return;

        // Consumer type toggle functionality
        $form.find('input[name="consumer_type"]').on('change', function() {
            const consumerType = $(this).val();
            const $individualFields = $('#individual-fields');
            const $corporateFields = $('#corporate-fields');
            
            if (consumerType === 'corporate') {
                $individualFields.addClass('hidden');
                $corporateFields.removeClass('hidden');
                
                // Enable corporate fields and make them required
                $corporateFields.find('input').prop('disabled', false).attr('required', true);
                // Disable individual fields and remove required attribute
                $individualFields.find('input').prop('disabled', true).removeAttr('required');
                
                // Clear any validation errors from hidden fields
                $individualFields.find('input').removeClass('border-red-500 border-green-500').addClass('border-gray-300');
                $individualFields.find('.field-feedback').addClass('hidden');
            } else {
                $individualFields.removeClass('hidden');
                $corporateFields.addClass('hidden');
                
                // Enable individual fields and make them required
                $individualFields.find('input').prop('disabled', false).attr('required', true);
                // Disable corporate fields and remove required attribute
                $corporateFields.find('input').prop('disabled', true).removeAttr('required');
                
                // Clear any validation errors from hidden fields
                $corporateFields.find('input').removeClass('border-red-500 border-green-500').addClass('border-gray-300');
                $corporateFields.find('.field-feedback').addClass('hidden');
            }
        });

        // Initialize the form state
        $form.find('input[name="consumer_type"]:checked').trigger('change');

        // Username validation with debounce
        const debouncedUsernameCheck = debounce(function(username, field) {
            validateUsername(username, function(message, type) {
                showFieldFeedback(field, message, type);
            });
        }, 500);

        // Note: Password validation is handled by initBusinessRegistration() for all forms

        $form.find('#username').on('input blur', function() {
            const username = $(this).val().trim();
            const field = this;
            
            if (username) {
                debouncedUsernameCheck(username, field);
            } else {
                clearFieldFeedback(field);
            }
        });

        // Email validation with debounce
        const debouncedEmailCheck = debounce(function(email, field) {
            validateEmail(email, function(message, type) {
                showFieldFeedback(field, message, type);
            });
        }, 500);

        $form.find('#email').on('input blur', function() {
            const email = $(this).val().trim();
            const field = this;
            
            if (email) {
                debouncedEmailCheck(email, field);
            } else {
                clearFieldFeedback(field);
            }
        });

        // Phone validation
        $form.find('#phone').on('input blur', function() {
            const phone = $(this).val().trim();
            const result = validatePhone(phone);
            
            if (phone) {
                showFieldFeedback(this, result.message, result.valid ? 'success' : 'error');
            } else {
                clearFieldFeedback(this);
            }
        });

        // Note: Password validation is handled by initBusinessRegistration() for all forms

        // Note: Name field validation is handled by initBusinessRegistration() for all forms
    }

    // Initialize validation for all registration forms (renamed for clarity)
    function initBusinessRegistration() {
        let $form = $('#autohub-business-owner-registration-form, #autohub-registration-form');
        
        // Fallback: try to find ANY form with password fields
        if (!$form.length) {
            $form = $('form').filter(function() {
                return $(this).find('input[name="password"], #consumer_password, #bo_password').length > 0;
            });
        }
        
        if (!$form.length) return;

        // Username validation with debounce (supports both business forms)
        const debouncedUsernameCheck = debounce(function(username, field) {
            validateUsername(username, function(message, type) {
                showFieldFeedback(field, message, type);
            });
        }, 500);

        $form.find('#consumer_username, #bo_username').on('input blur', function() {
            const username = $(this).val().trim();
            const field = this;
            
            if (username) {
                debouncedUsernameCheck(username, field);
            } else {
                // Show required message when field is empty and loses focus
                showFieldFeedback(field, 'Username is required', 'error');
            }
        });

        // Email validation with debounce (supports both business forms)
        const debouncedEmailCheck = debounce(function(email, field) {
            validateEmail(email, function(message, type) {
                showFieldFeedback(field, message, type);
            });
        }, 500);

        $form.find('#email, #bo_email').on('input blur', function() {
            const email = $(this).val().trim();
            const field = this;
            
            if (email) {
                debouncedEmailCheck(email, field);
            } else {
                // Show required message when field is empty and loses focus
                showFieldFeedback(field, 'Email address is required', 'error');
            }
        });

        // Phone validation for all business phone fields
        $form.find('#phone, #bo_phone').on('input blur', function() {
            const phone = $(this).val().trim();
            const result = validatePhone(phone);
            
            if (phone) {
                showFieldFeedback(this, result.message, result.valid ? 'success' : 'error');
            } else {
                // Show required message when field is empty and loses focus
                showFieldFeedback(this, 'Phone number is required', 'error');
            }
        });

        // Password validation with real-time feedback (supports all forms)
        const $passwordFields = $form.find('input[name="password"], #consumer_password, #bo_password');

        

        
        $passwordFields.on('focus', function() {
            // Focus handler
        });
        
        $passwordFields.on('input keyup', function() {

            const password = $(this).val();

            const passwordValidation = validatePassword(password);

            const $strengthIndicator = $(this).closest('.form-group').find('.password-strength-indicator');
            const $strengthText = $strengthIndicator.find('.strength-text');

            const $strengthBars = $strengthIndicator.find('.strength-bar');
            
            // Update strength indicator
            $strengthIndicator.removeClass('strength-weak strength-medium strength-strong');
            if (passwordValidation.strength !== 'none') {
                $strengthIndicator.addClass('strength-' + passwordValidation.strength);
                $strengthText.text(passwordValidation.strength.charAt(0).toUpperCase() + passwordValidation.strength.slice(1));
                
                // Update strength bars
                $strengthBars.removeClass('bg-red-500 bg-orange-500 bg-green-500').addClass('bg-gray-200');
                if (passwordValidation.strength === 'weak') {
                    $strengthBars.eq(0).removeClass('bg-gray-200').addClass('bg-red-500');
                } else if (passwordValidation.strength === 'medium') {
                    $strengthBars.eq(0).removeClass('bg-gray-200').addClass('bg-orange-500');
                    $strengthBars.eq(1).removeClass('bg-gray-200').addClass('bg-orange-500');
                } else if (passwordValidation.strength === 'strong') {
                    $strengthBars.removeClass('bg-gray-200').addClass('bg-green-500');
                }
            } else {
                $strengthText.text('');
                $strengthBars.removeClass('bg-red-500 bg-orange-500 bg-green-500').addClass('bg-gray-200');
            }
            
            // Show validation feedback
            if (password.length > 0) {
                showFieldFeedback(this, passwordValidation.message, passwordValidation.valid ? 'success' : 'error');
            } else {
                clearFieldFeedback(this);
            }

            // Also validate confirm password if it has a value
            const confirmPassword = $form.find('input[name="confirm_password"], #consumer_confirm_password, #bo_confirm_password').val();
            if (confirmPassword) {
                const confirmResult = validateConfirmPassword(password, confirmPassword);
                const confirmField = $form.find('input[name="confirm_password"], #consumer_confirm_password, #bo_confirm_password')[0];
                if (confirmField) {
                    showFieldFeedback(confirmField, confirmResult.message, confirmResult.valid ? 'success' : 'error');
                }
            }
        });

        // Password validation on blur (when user leaves field)
        $form.find('input[name="password"], #consumer_password, #bo_password').on('blur', function() {
            const password = $(this).val();
            if (password.length === 0) {
                showFieldFeedback(this, 'Password is required', 'error');
            }
        });

        // Confirm password validation (supports all forms)
        $form.find('input[name="confirm_password"], #consumer_confirm_password, #bo_confirm_password').on('input keyup', function() {
            const password = $form.find('input[name="password"], #consumer_password, #bo_password').val();
            const confirmPassword = $(this).val();
            
            if (confirmPassword.length > 0) {
                const confirmValidation = validateConfirmPassword(password, confirmPassword);
                showFieldFeedback(this, confirmValidation.message, confirmValidation.valid ? 'success' : 'error');
            } else {
                clearFieldFeedback(this);
            }
        });

        // Confirm password validation on blur (when user leaves field)
        $form.find('input[name="confirm_password"], #consumer_confirm_password, #bo_confirm_password').on('blur', function() {
            const confirmPassword = $(this).val();
            if (confirmPassword.length === 0) {
                showFieldFeedback(this, 'Please confirm your password', 'error');
            }
        });

        // Name fields validation (supports all forms)
        $form.find('#first_name, #last_name, #corporate_name, #contact_first_name, #contact_last_name, #bo_first_name, #bo_last_name').on('input blur', function() {
            const value = $(this).val().trim();
            const fieldId = $(this).attr('id');
            let fieldName = 'firstName';
            
            // Determine field type
            if (fieldId === 'last_name' || fieldId === 'contact_last_name' || fieldId === 'bo_last_name') {
                fieldName = 'lastName';
            } else if (fieldId === 'corporate_name') {
                fieldName = 'corporateName';
            }
            
            const result = validateRequiredText(value, fieldName);
            
            if (value) {
                showFieldFeedback(this, result.message, result.valid ? 'success' : 'error');
            } else {
                // Show required message when field is empty and loses focus
                let message = 'First name is required';
                if (fieldId === 'last_name' || fieldId === 'contact_last_name' || fieldId === 'bo_last_name') {
                    message = 'Last name is required';
                } else if (fieldId === 'corporate_name') {
                    message = 'Corporate name is required';
                }
                showFieldFeedback(this, message, 'error');
            }
        });

        // Business name validation
        $form.find('#business_name').on('input blur', function() {
            const value = $(this).val().trim();
            const result = validateRequiredText(value, 'businessName');
            
            if (value) {
                showFieldFeedback(this, result.message, result.valid ? 'success' : 'error');
            } else {
                clearFieldFeedback(this);
            }
        });
    }

    // Update password strength indicator
    function updatePasswordStrength(strength) {
        const $indicator = $('.password-strength-indicator');
        if (!$indicator.length) return;

        $indicator.removeClass('strength-none strength-weak strength-medium strength-strong');
        $indicator.addClass('strength-' + strength);

        const strengthText = {
            'none': '',
            'weak': 'Weak',
            'medium': 'Medium',
            'strong': 'Strong'
        };

        $indicator.find('.strength-text').text(strengthText[strength]);
    }

    // Password toggle functionality
    function initPasswordToggle() {
        $(document).on('click', '.password-toggle', function() {
            const $button = $(this);
            const targetId = $button.data('target');
            const $input = $('#' + targetId);
            const $eyeOpen = $button.find('.eye-open');
            const $eyeClosed = $button.find('.eye-closed');

            if ($input.attr('type') === 'password') {
                $input.attr('type', 'text');
                $eyeOpen.addClass('hidden');
                $eyeClosed.removeClass('hidden');
            } else {
                $input.attr('type', 'password');
                $eyeOpen.removeClass('hidden');
                $eyeClosed.addClass('hidden');
            }
        });
    }

    // Form submission validation
    function validateFormBeforeSubmit($form) {
        let isValid = true;
        const errors = [];

        // Check all required fields
        $form.find('[required]').each(function() {
            const $field = $(this);
            const value = $field.val().trim();

            if (!value) {
                const label = $field.closest('.form-group').find('label').text().replace('*', '').trim();
                errors.push(label + ' is required');
                showFieldFeedback(this, 'This field is required', 'error');
                isValid = false;
            }
        });

        // Validate password strength specifically
        const $passwordField = $form.find('input[name="password"]');
        if ($passwordField.length) {
            const password = $passwordField.val();
            const passwordValidation = validatePassword(password);
            
            if (!passwordValidation.valid) {
                errors.push('Password does not meet requirements');
                showFieldFeedback($passwordField[0], passwordValidation.message, 'error');
                isValid = false;
            }
        }

        // Validate confirm password
        const $confirmPasswordField = $form.find('input[name="confirm_password"]');
        if ($confirmPasswordField.length && $passwordField.length) {
            const password = $passwordField.val();
            const confirmPassword = $confirmPasswordField.val();
            const confirmValidation = validateConfirmPassword(password, confirmPassword);
            
            if (!confirmValidation.valid) {
                errors.push('Password confirmation does not match');
                showFieldFeedback($confirmPasswordField[0], confirmValidation.message, 'error');
                isValid = false;
            }
        }

        // Check for existing validation errors
        $form.find('.border-red-500').each(function() {
            isValid = false;
        });

        return { isValid, errors };
    }

    // Load plan costs for business registration
    function loadPlanCosts() {
        // Check if we have the plan cost display elements on the page
        const $planCostDisplay = $('#plan-cost-display');
        const $registrationTypeInputs = $('input[name="registration_type"]');
        
        if ($planCostDisplay.length === 0 || $registrationTypeInputs.length === 0) {
            return; // No plan elements found, skip loading
        }
        
        // Check if AJAX object is available
        if (typeof autohub_reg_ajax === 'undefined') {
            console.warn('AutoHub Registration: AJAX object not available for plan costs');
            return;
        }
        
        // Store plan data globally for use when radio buttons change
        let planCosts = {};
        
        // Make AJAX request to get plan costs
        $.ajax({
            url: autohub_reg_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'autohub_get_plan_costs',
                nonce: autohub_reg_ajax.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    planCosts = response.data;
                    
                    // Update display if a radio button is already selected
                    const $selectedInput = $registrationTypeInputs.filter(':checked');
                    if ($selectedInput.length) {
                        showPlanCost($selectedInput.val());
                    }
                } else {
                    console.error('AutoHub Registration: Failed to load plan costs:', response);
                }
            },
            error: function(xhr, status, error) {
                console.error('AutoHub Registration: Error loading plan costs:', error);
            }
        });
        
        // Function to show plan cost
        function showPlanCost(type) {
            const planPricingContent = document.getElementById('plan-pricing-content');
            const planKey = type === 'auto_shop' ? 'auto_shop' : 'vehicle_professional';
            
            if (planCosts[planKey]) {
                // Use innerHTML to properly render the HTML content
                planPricingContent.innerHTML = planCosts[planKey];
                $planCostDisplay.removeClass('hidden');
            } else {
                planPricingContent.innerHTML = '<div class="text-gray-600">Loading pricing...</div>';
                $planCostDisplay.removeClass('hidden');
            }
        }
        
        // Handle registration type selection
        $registrationTypeInputs.on('change', function() {
            if (this.checked) {
                showPlanCost(this.value);
                
                // Update visual selection state
                $('.registration-type-option').removeClass('border-primary bg-primary-50').addClass('border-gray-300');
                
                const $selectedOption = $(this).closest('.registration-type-option');
                $selectedOption.removeClass('border-gray-300').addClass('border-primary bg-primary-50');
            }
        });
    }

    // Handle resend verification email functionality
    function initResendVerification() {
        // Handle resend verification button click
        $(document).on('click', '#resend-verification-btn', function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const $messageDiv = $('#resend-verification-message');
            const userEmail = $btn.data('user-email');
            
            if (!userEmail) {

                $messageDiv.html('<div style="color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;">Error: No email address found</div>').show();
                return;
            }
            
            if (typeof autohub_reg_ajax === 'undefined') {

                $messageDiv.html('<div style="color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;">Error: AJAX not configured</div>').show();
                return;
            }
            
            // Show loading state
            $btn.prop('disabled', true).text('Sending...');
            $messageDiv.hide();
            
            // Make AJAX request
            $.ajax({
                url: autohub_reg_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'autohub_resend_verification',
                    email: userEmail,
                    nonce: autohub_reg_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $messageDiv.html('<div style="color: #2e7d32; padding: 10px; background: #e8f5e8; border-radius: 4px;">' + response.data.message + '</div>').show();
                        $btn.text('Email Sent!');
                        
                        // Re-enable button after 30 seconds
                        setTimeout(function() {
                            $btn.prop('disabled', false).text('Resend Verification Email');
                        }, 30000);
                    } else {
                        $messageDiv.html('<div style="color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;">' + (response.data.message || 'Failed to send verification email') + '</div>').show();
                        $btn.prop('disabled', false).text('Resend Verification Email');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AutoHub: AJAX error:', error);
                    console.error('AutoHub: XHR response:', xhr.responseText);
                    $messageDiv.html('<div style="color: #d32f2f; padding: 10px; background: #ffebee; border-radius: 4px;">Connection error. Please try again.</div>').show();
                    $btn.prop('disabled', false).text('Resend Verification Email');
                }
            });
        });
    }

    // Initialize all functionality
    $(document).ready(function() {
        // Try to initialize forms
        initConsumerRegistration();
        initBusinessRegistration();
        initPasswordToggle();
        initResendVerification();
        initVerificationStatus();
        
        // Load plan costs for business registration form
        loadPlanCosts();
        
        // Fallback: Initialize validation on any form with registration fields
        const $anyRegForm = $('form').filter(function() {
            const hasUsername = $(this).find('input[name="username"]').length > 0;
            const hasEmail = $(this).find('input[name="email"], input[name="business_email"]').length > 0;
            const hasPassword = $(this).find('input[name="password"]').length > 0;
            return hasUsername && hasEmail && hasPassword;
        });

        // Handle consumer registration form submission with AJAX
        $('#autohub-registration-form').on('submit', function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $submitBtn = $form.find('button[type="submit"]');
            const $submitText = $submitBtn.find('.btn-text');
            const $loadingText = $submitBtn.find('.btn-loading');
            const $messagesDiv = $('#registration-messages');
            
            // Validate form before submission
            const validation = validateFormBeforeSubmit($form);
            
            if (!validation.isValid) {
                // Show specific error messages
                let errorHtml = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">';
                errorHtml += '<div class="font-medium mb-2">Please fix the following errors:</div>';
                errorHtml += '<ul class="list-disc list-inside space-y-1">';
                
                validation.errors.forEach(function(error) {
                    errorHtml += '<li>' + error + '</li>';
                });
                
                errorHtml += '</ul></div>';
                $messagesDiv.html(errorHtml);
                $messagesDiv.removeClass('hidden');
                
                // Scroll to first error
                const $firstError = $form.find('.border-red-500').first();
                if ($firstError.length) {
                    $firstError[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                    $firstError.focus();
                }
                return;
            }
            
            // Show loading state
            $submitBtn.prop('disabled', true);
            $submitText.addClass('hidden');
            $loadingText.removeClass('hidden');
            $messagesDiv.addClass('hidden');
            
            // Prepare form data
            const formData = new FormData($form[0]);
            formData.append('action', 'autohub_register_user');
            
            // Submit form via AJAX
            $.ajax({
                url: autohub_reg_ajax.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        $messagesDiv.html('<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">' + 
                            '<div class="font-medium mb-2">Registration Successful!</div>' +
                            '<p>' + response.data.message + '</p>' +
                            '</div>');
                        $messagesDiv.removeClass('hidden');
                        
                        // Reset form
                        $form[0].reset();
                        
                        // Clear all field feedback
                        $form.find('.field-feedback').addClass('hidden');
                        $form.find('input').removeClass('border-red-500 border-green-500').addClass('border-gray-300');
                        
                        // Reset password strength indicator
                        $form.find('.password-strength-indicator').removeClass('strength-weak strength-medium strength-strong');
                        $form.find('.strength-text').text('');
                        $form.find('.strength-bar').removeClass('bg-red-500 bg-orange-500 bg-green-500').addClass('bg-gray-200');
                        
                        // Reset consumer type to individual and show/hide fields accordingly
                        $form.find('input[name="consumer_type"][value="individual"]').prop('checked', true);
                        $('#individual-fields').removeClass('hidden');
                        $('#corporate-fields').addClass('hidden');
                        
                        // Scroll to success message
                        $messagesDiv[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                        
                    } else {
                        // Show error message
                        $messagesDiv.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">' + 
                            '<div class="font-medium mb-2">Registration Failed</div>' +
                            '<p>' + (response.data.message || 'An error occurred. Please try again.') + '</p>' +
                            '</div>');
                        $messagesDiv.removeClass('hidden');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Consumer registration error:', error);
                    // Show error message
                    let errorMessage = 'Connection Error: ';
                    if (xhr.status === 500) {
                        errorMessage += 'Server error occurred. Please try again or contact support.';
                    } else if (xhr.status === 0) {
                        errorMessage += 'Unable to connect to the server. Please check your internet connection and try again.';
                    } else {
                        errorMessage += 'An unexpected error occurred. Please try again.';
                    }
                    
                    $messagesDiv.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">' + 
                        '<div class="font-medium mb-2">Registration Failed</div>' +
                        '<p>' + errorMessage + '</p>' +
                        '</div>');
                    $messagesDiv.removeClass('hidden');
                },
                complete: function() {
                    // Reset button state
                    $submitBtn.prop('disabled', false);
                    $submitText.removeClass('hidden');
                    $loadingText.addClass('hidden');
                }
            });
        });

        // Handle business owner registration form submission with AJAX
        $('#autohub-business-owner-registration-form').on('submit', function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $submitBtn = $form.find('button[type="submit"]');
            const $submitText = $submitBtn.find('.submit-text');
            const $loadingText = $submitBtn.find('.loading-text');
            const $messagesDiv = $('#form-messages');
            
            // Validate form before submission
            const validation = validateFormBeforeSubmit($form);
            
            if (!validation.isValid) {
                // Show specific error messages
                let errorHtml = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">';
                errorHtml += '<div class="font-medium mb-2">Please fix the following errors:</div>';
                errorHtml += '<ul class="list-disc list-inside space-y-1">';
                
                validation.errors.forEach(function(error) {
                    errorHtml += '<li>' + error + '</li>';
                });
                
                errorHtml += '</ul></div>';
                $messagesDiv.html(errorHtml);
                $messagesDiv.removeClass('hidden');
                
                // Scroll to first error
                const $firstError = $form.find('.border-red-500').first();
                if ($firstError.length) {
                    $firstError[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                    $firstError.focus();
                }
                return;
            }
            
            // Show loading state
            $submitBtn.prop('disabled', true);
            $submitText.addClass('hidden');
            $loadingText.removeClass('hidden');
            $messagesDiv.addClass('hidden');
            
            // Prepare form data
            const formData = new FormData($form[0]);
            formData.append('action', 'autohub_register_business_owner');
            
            // Submit form via AJAX
            $.ajax({
                url: autohub_reg_ajax.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        $messagesDiv.html('<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">' + 
                            '<div class="font-medium mb-2">Registration Successful!</div>' +
                            '<p>' + response.data.message + '</p>' +
                            '</div>');
                        $messagesDiv.removeClass('hidden');
                        
                        // Reset form
                        $form[0].reset();
                        
                        // Clear all field feedback
                        $form.find('.field-feedback').addClass('hidden');
                        $form.find('input').removeClass('border-red-500 border-green-500').addClass('border-gray-300');
                        
                        // Reset password strength indicator
                        $form.find('.password-strength-indicator').removeClass('strength-weak strength-medium strength-strong');
                        $form.find('.strength-text').text('');
                        $form.find('.strength-bar').removeClass('bg-red-500 bg-orange-500 bg-green-500').addClass('bg-gray-200');
                        
                        // Scroll to success message
                        $messagesDiv[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                        
                    } else {
                        // Show error message
                        $messagesDiv.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">' + 
                            '<div class="font-medium mb-2">Registration Failed</div>' +
                            '<p>' + (response.data.message || 'An error occurred. Please try again.') + '</p>' +
                            '</div>');
                        $messagesDiv.removeClass('hidden');
                    }
                },
                error: function() {
                    // Show error message
                    $messagesDiv.html('<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">' + 
                        '<div class="font-medium mb-2">Connection Error</div>' +
                        '<p>Unable to connect to the server. Please check your internet connection and try again.</p>' +
                        '</div>');
                    $messagesDiv.removeClass('hidden');
                },
                complete: function() {
                    // Reset button state
                    $submitBtn.prop('disabled', false);
                    $submitText.removeClass('hidden');
                    $loadingText.addClass('hidden');
                }
            });
        });

        // Form submission validation - try specific IDs first, then fallback to any registration form
        let $submissionForms = $('#autohub-registration-form, #autohub-business-registration-form');
        if (!$submissionForms.length) {
            $submissionForms = $('form').filter(function() {
                const hasUsername = $(this).find('input[name="username"]').length > 0;
                const hasEmail = $(this).find('input[name="email"], input[name="business_email"]').length > 0;
                const hasPassword = $(this).find('input[name="password"]').length > 0;
                const formId = $(this).attr('id');
                // Exclude business owner form as it has its own handler
                return hasUsername && hasPassword && formId !== 'autohub-business-owner-registration-form';
            });
        }
        
        $submissionForms.on('submit', function(e) {
            const validation = validateFormBeforeSubmit($(this));
            
            if (!validation.isValid) {
                e.preventDefault();
                
                // Show specific error messages
                const $messagesDiv = $(this).find('[id$="-messages"]');
                if ($messagesDiv.length) {
                    let errorHtml = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">';
                    errorHtml += '<div class="font-medium mb-2">Please fix the following errors:</div>';
                    errorHtml += '<ul class="list-disc list-inside space-y-1">';
                    
                    validation.errors.forEach(function(error) {
                        errorHtml += '<li>' + error + '</li>';
                    });
                    
                    errorHtml += '</ul></div>';
                    $messagesDiv.html(errorHtml);
                    $messagesDiv.removeClass('hidden');
                }

                // Scroll to first error
                const $firstError = $(this).find('.border-red-500').first();
                if ($firstError.length) {
                    $firstError[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                    $firstError.focus();
                }
            }
        });
    });

    // Initialize verification status functionality
    function initVerificationStatus() {
        // Check if verification status container exists (try multiple selectors)
        const $container = $('.autohub-verification-status');
        const $statusDisplay = $('#status-display');
        const $actionButtons = $('#action-buttons');
        
        if (!$container.length && !$statusDisplay.length) {
            return;
        }

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const email = urlParams.get('email');
        const token = urlParams.get('token');
        
        // Check for error parameter
        const errorParam = urlParams.get('error');
        if (errorParam === 'invalid_link') {
            showError('Invalid or expired verification link. Please request a new verification email.');
            return;
        }
        
        // Check if we have the required parameters
        if (!email) {
            showError('Missing email parameter. Please use the link from your verification email or enter your email below to check status.');
            // Show a simple email input form for manual status check
            showEmailInputForm();
            return;
        }
        
        // Check verification status
        checkVerificationStatus();
        
        function checkVerificationStatus() {
            $.ajax({
                url: autohub_reg_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'autohub_check_verification_status',
                    email: email,
                    token: token || ''
                },
                success: function(response) {
                    if (response.success) {
                        displayStatus(response.data);
                    } else {
                        showError(response.data.message || 'Failed to check verification status');
                    }
                },
                error: function() {
                    showError('Connection error. Please try again.');
                }
            });
        }
        
        function displayStatus(statusData) {
            const $statusDisplay = $('#status-display');
            const $actionButtons = $('#action-buttons');
            
            let statusHtml = '';
            let statusClass = '';
            
            switch (statusData.status) {
                case 'approved':
                    statusClass = 'bg-green-100 border-green-400 text-green-700';
                    statusHtml = `
                        <div class="flex items-center justify-center mb-4">
                            <svg class="h-16 w-16 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-green-700 mb-2">Account Activated!</h2>
                        <p class="text-green-600">${statusData.message}</p>
                    `;
                    $('#login-section').removeClass('hidden');
                    break;
                    
                case 'pending_approval':
                    statusClass = 'bg-yellow-100 border-yellow-400 text-yellow-700';
                    statusHtml = `
                        <div class="flex items-center justify-center mb-4">
                            <svg class="h-16 w-16 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-yellow-700 mb-2">Awaiting Approval</h2>
                        <p class="text-yellow-600">${statusData.message}</p>
                    `;
                    break;
                    
                case 'verified':
                    statusClass = 'bg-blue-100 border-blue-400 text-blue-700';
                    statusHtml = `
                        <div class="flex items-center justify-center mb-4">
                            <svg class="h-16 w-16 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-blue-700 mb-2">Email Verified</h2>
                        <p class="text-blue-600">${statusData.message}</p>
                    `;
                    $('#login-section').removeClass('hidden');
                    break;
                    
                case 'unverified':
                    statusClass = 'bg-red-100 border-red-400 text-red-700';
                    statusHtml = `
                        <div class="flex items-center justify-center mb-4">
                            <svg class="h-16 w-16 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-red-700 mb-2">Email Not Verified</h2>
                        <p class="text-red-600">${statusData.message}</p>
                    `;
                    
                    // Show resend and change email options
                    if (statusData.can_resend) {
                        $('#resend-section').removeClass('hidden');
                    }
                    $('#change-email-section').removeClass('hidden');
                    $('#current-email').val(email);
                    
                    // Show attempt info if available
                    if (statusData.verification_attempts) {
                        statusHtml += `<p class="text-sm text-red-600 mt-2">Verification attempts: ${statusData.verification_attempts}/3</p>`;
                    }
                    break;
            }
            
            $statusDisplay.html(`<div class="border rounded-lg p-6 text-center ${statusClass}">${statusHtml}</div>`);
            $actionButtons.removeClass('hidden');
        }
        
        // Handle resend verification
        $(document).on('click', '#resend-verification-btn', function() {
            const $btn = $(this);
            $btn.prop('disabled', true).text('Sending...');
            
            $.ajax({
                url: autohub_reg_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'autohub_resend_verification',
                    email: email,
                    nonce: autohub_reg_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showSuccess(response.data.message);
                        $btn.text('Email Sent!');
                        setTimeout(() => {
                            $btn.prop('disabled', false).text('Resend Verification Email');
                        }, 30000);
                    } else {
                        showError(response.data.message || 'Failed to send verification email');
                        $btn.prop('disabled', false).text('Resend Verification Email');
                    }
                },
                error: function() {
                    showError('Connection error. Please try again.');
                    $btn.prop('disabled', false).text('Resend Verification Email');
                }
            });
        });
        
        // Handle email change
        $(document).on('submit', '#change-email-form', function(e) {
            e.preventDefault();
            
            const newEmail = $('#new-email').val();
            const $btn = $('#change-email-btn');
            
            if (!newEmail || !isValidEmail(newEmail)) {
                showError('Please enter a valid email address');
                return;
            }
            
            if (newEmail === email) {
                showError('New email must be different from current email');
                return;
            }
            
            $btn.prop('disabled', true).text('Changing...');
            
            $.ajax({
                url: autohub_reg_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'autohub_change_pending_email',
                    old_email: email,
                    new_email: newEmail,
                    token: token || '',
                    nonce: autohub_reg_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showSuccess(response.data.message);
                        // Update the URL and reload status
                        const newUrl = new URL(window.location);
                        newUrl.searchParams.set('email', newEmail);
                        window.history.replaceState({}, '', newUrl);
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        showError(response.data.message || 'Failed to change email address');
                        $btn.prop('disabled', false).text('Change Email Address');
                    }
                },
                error: function() {
                    showError('Connection error. Please try again.');
                    $btn.prop('disabled', false).text('Change Email Address');
                }
            });
        });
        
        function showSuccess(message) {
            showMessage(message, 'success');
        }
        
        function showError(message) {
            showMessage(message, 'error');
        }
        
        function showMessage(message, type) {
            const $messagesDiv = $('#messages');
            const alertClass = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';
            
            $messagesDiv.html(`
                <div class="border rounded-lg p-4 ${alertClass}">
                    <p>${message}</p>
                </div>
            `);
            
            // Auto-hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(() => {
                    $messagesDiv.html('');
                }, 5000);
            }
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showEmailInputForm() {
            const $statusDisplay = $('#status-display');
            const $actionButtons = $('#action-buttons');
            
            $statusDisplay.html(`
                <div class="border rounded-lg p-6 text-center bg-blue-50 border-blue-200">
                    <h2 class="text-xl font-bold text-blue-700 mb-4">Check Your Verification Status</h2>
                    <p class="text-blue-600 mb-4">Enter your email address to check your verification status:</p>
                    <form id="email-check-form" class="space-y-4">
                        <div>
                            <input type="email" id="email-check-input" placeholder="Enter your email address" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        <button type="submit" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition duration-200">
                            Check Status
                        </button>
                    </form>
                </div>
            `);
            
            // Handle form submission
            $(document).on('submit', '#email-check-form', function(e) {
                e.preventDefault();
                const enteredEmail = $('#email-check-input').val().trim();
                
                if (!enteredEmail || !isValidEmail(enteredEmail)) {
                    showError('Please enter a valid email address');
                    return;
                }
                
                // Update URL and reload with email parameter
                const newUrl = new URL(window.location);
                newUrl.searchParams.set('email', enteredEmail);
                window.location.href = newUrl.toString();
            });
            
            $actionButtons.removeClass('hidden');
        }
    }

})(jQuery);