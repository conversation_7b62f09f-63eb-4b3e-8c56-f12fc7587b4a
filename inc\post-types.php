<?php
/**
 * Custom Post Types
 *
 * @package AutoHub_Zambia
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register custom post type 'listing'
 */
function autohub_register_listing_post_type() {
    // Listing Post Type
    $labels = array(
        'name' => 'Listings',
        'singular_name' => 'Listing',
        'add_new' => 'Add New Listing',
        'add_new_item' => 'Add New Listing',
        'edit_item' => 'Edit Listing',
        'new_item' => 'New Listing',
        'view_item' => 'View Listing',
        'search_items' => 'Search Listings',
        'not_found' => 'No Listings found',
        'not_found_in_trash' => 'No Listings found in Trash',
        'all_items' => 'All Listings',
        'menu_name' => 'Listings',
    );

    register_post_type('listing', array(
        'labels' => $labels,
        'public' => true,
        'has_archive' => true,
        'rewrite' => array('slug' => 'listings'),
        'supports' => array('title', 'editor', 'thumbnail'),
        'show_in_rest' => true,
        'capability_type' => 'listing',
        'map_meta_cap' => true,
        'menu_icon' => 'dashicons-car',
        'menu_position' => 20,
    ));
}
add_action('init', 'autohub_register_listing_post_type', 0);



/**
 * Register custom post type 'my_vehicle'
 */
function autohub_register_my_vehicle_post_type() {
    // My Vehicle Post Type
    register_post_type('my_vehicle', array(
        'labels' => array(
            'name' => 'My Vehicles',
            'singular_name' => 'My Vehicle',
            'menu_name' => 'My Vehicles',
            'add_new' => 'Add New Vehicle',
            'add_new_item' => 'Add New Vehicle',
            'edit_item' => 'Edit Vehicle',
            'new_item' => 'New Vehicle',
            'view_item' => 'View Vehicle',
            'search_items' => 'Search Vehicles',
            'not_found' => 'No vehicles found',
            'not_found_in_trash' => 'No vehicles found in trash'
        ),
        'public' => false, // private to logged-in users
        'hierarchical' => false,
        'supports' => array('title'),
        'has_archive' => false,
        'show_ui' => true,
        'rewrite' => array('slug' => 'my-vehicle'),
        'menu_icon' => 'dashicons-car',
        'menu_position' => 22,
        'capability_type' => 'my_vehicle',
        'map_meta_cap' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'autohub_register_my_vehicle_post_type', 0);

/**
 * Register custom post type 'request'
 */
function autohub_register_request_post_type() {
    // Request Post Type
    register_post_type('request', array(
        'labels' => array(
            'name' => 'Requests',
            'singular_name' => 'Request',
            'menu_name' => 'Requests',
            'add_new' => 'Add New Request',
            'add_new_item' => 'Add New Request',
            'edit_item' => 'Edit Request',
            'new_item' => 'New Request',
            'view_item' => 'View Request',
            'search_items' => 'Search Requests',
            'not_found' => 'No requests found',
            'not_found_in_trash' => 'No requests found in trash'
        ),
        'public' => true,
        'hierarchical' => false,
        'supports' => array('title', 'editor'),
        'has_archive' => true,
        'rewrite' => array('slug' => 'request'),
        'menu_icon' => 'dashicons-search',
        'menu_position' => 23,
        'capability_type' => 'request',
        'map_meta_cap' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'autohub_register_request_post_type', 0);

/**
 * Register custom post type 'quote'
 */
function autohub_register_quote_post_type() {
    // Quote Post Type
    register_post_type('quote', array(
        'labels' => array(
            'name' => 'Quotes',
            'singular_name' => 'Quote',
            'menu_name' => 'Quotes',
            'add_new' => 'Add New Quote',
            'add_new_item' => 'Add New Quote',
            'edit_item' => 'Edit Quote',
            'new_item' => 'New Quote',
            'view_item' => 'View Quote',
            'search_items' => 'Search Quotes',
            'not_found' => 'No quotes found',
            'not_found_in_trash' => 'No quotes found in trash'
        ),
        'public' => true,
        'hierarchical' => false,
        'supports' => array('title', 'editor'),
        'has_archive' => true,
        'rewrite' => array('slug' => 'quote'),
        'menu_icon' => 'dashicons-format-quote',
        'menu_position' => 23.5,
        'capability_type' => 'quote',
        'map_meta_cap' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'autohub_register_quote_post_type', 0);

/**
 * Register custom post type 'review'
 */
function autohub_register_review_post_type() {
    // Review Post Type
    register_post_type('review', array(
        'labels' => array(
            'name' => 'Reviews',
            'singular_name' => 'Review',
            'menu_name' => 'Reviews',
            'add_new' => 'Add New Review',
            'add_new_item' => 'Add New Review',
            'edit_item' => 'Edit Review',
            'new_item' => 'New Review',
            'view_item' => 'View Review',
            'search_items' => 'Search Reviews',
            'not_found' => 'No reviews found',
            'not_found_in_trash' => 'No reviews found in trash'
        ),
        'public' => true,
        'hierarchical' => false,
        'supports' => array('title', 'editor'),
        'has_archive' => true,
        'rewrite' => array('slug' => 'review'),
        'menu_icon' => 'dashicons-star-filled',
        'menu_position' => 24,
        'capability_type' => 'review',
        'map_meta_cap' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'autohub_register_review_post_type', 0);

/**
 * Register custom post type 'province'
 */
function autohub_register_province_post_type() {
    // Province Post Type
    register_post_type('province', array(
        'labels' => array(
            'name' => 'Provinces',
            'singular_name' => 'Province',
            'menu_name' => 'Provinces',
            'add_new' => 'Add New Province',
            'add_new_item' => 'Add New Province',
            'edit_item' => 'Edit Province',
            'new_item' => 'New Province',
            'view_item' => 'View Province',
            'search_items' => 'Search Provinces',
            'not_found' => 'No provinces found',
            'not_found_in_trash' => 'No provinces found in trash'
        ),
        'public' => true,
        'hierarchical' => false,
        'supports' => array('title'),
        'has_archive' => false,
        'rewrite' => array('slug' => 'province'),
        'menu_icon' => 'dashicons-location',
        'menu_position' => 25,
        'capability_type' => 'province',
        'map_meta_cap' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'autohub_register_province_post_type', 0);

/**
 * Register custom post type 'city_town'
 */
function autohub_register_city_town_post_type() {
    // City/Town Post Type
    register_post_type('city_town', array(
        'labels' => array(
            'name' => 'Cities/Towns',
            'singular_name' => 'City/Town',
            'menu_name' => 'Cities/Towns',
            'add_new' => 'Add New City/Town',
            'add_new_item' => 'Add New City/Town',
            'edit_item' => 'Edit City/Town',
            'new_item' => 'New City/Town',
            'view_item' => 'View City/Town',
            'search_items' => 'Search Cities/Towns',
            'not_found' => 'No cities/towns found',
            'not_found_in_trash' => 'No cities/towns found in trash'
        ),
        'public' => true,
        'hierarchical' => false,
        'supports' => array('title'),
        'has_archive' => false,
        'rewrite' => array('slug' => 'city-town'),
        'menu_icon' => 'dashicons-building',
        'menu_position' => 26,
        'capability_type' => 'city_town',
        'map_meta_cap' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'autohub_register_city_town_post_type', 0);

/**
 * Register custom post type 'auto_shop' (unified: garages, dealerships, body shops, parts sellers, etc.)
 */
function autohub_register_auto_shop_post_type() {
    // Auto Shop Post Type (unified structure)
    register_post_type('auto_shop', array(
        'labels' => array(
            'name' => 'Auto Shops',
            'singular_name' => 'Auto Shop',
            'menu_name' => 'Auto Shops',
            'add_new' => 'Add New Auto Shop',
            'add_new_item' => 'Add New Auto Shop',
            'edit_item' => 'Edit Auto Shop',
            'new_item' => 'New Auto Shop',
            'view_item' => 'View Auto Shop',
            'search_items' => 'Search Auto Shops',
            'not_found' => 'No auto shops found',
            'not_found_in_trash' => 'No auto shops found in trash'
        ),
        'public' => true,
        'hierarchical' => false,
        'supports' => array('title', 'editor', 'thumbnail'),
        'has_archive' => true,
        'rewrite' => array('slug' => 'auto-shops'),
        'menu_icon' => 'dashicons-admin-tools',
        'menu_position' => 27,
        'capability_type' => 'auto_shop',
        'map_meta_cap' => true,
        'show_in_rest' => true,
        'taxonomies' => array('auto_shop_category'),
    ));
}
add_action('init', 'autohub_register_auto_shop_post_type', 0);

/**
 * Register Auto Shop Category taxonomy
 */
function autohub_register_auto_shop_taxonomy() {
    $labels = array(
        'name' => 'Auto Shop Categories',
        'singular_name' => 'Auto Shop Category',
        'menu_name' => 'Categories',
        'all_items' => 'All Categories',
        'edit_item' => 'Edit Category',
        'view_item' => 'View Category',
        'update_item' => 'Update Category',
        'add_new_item' => 'Add New Category',
        'new_item_name' => 'New Category Name',
        'parent_item' => 'Parent Category',
        'parent_item_colon' => 'Parent Category:',
        'search_items' => 'Search Categories',
        'popular_items' => 'Popular Categories',
        'separate_items_with_commas' => 'Separate categories with commas',
        'add_or_remove_items' => 'Add or remove categories',
        'choose_from_most_used' => 'Choose from the most used categories',
        'not_found' => 'No categories found',
    );

    register_taxonomy('auto_shop_category', array('auto_shop'), array(
        'labels' => $labels,
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array(
            'slug' => 'auto-shop-category',
            'with_front' => false,
        ),
        'query_var' => true,
        'meta_box_cb' => 'post_categories_meta_box', // Use the default categories meta box for multiple selection
    ));
}
add_action('init', 'autohub_register_auto_shop_taxonomy', 0);

/**
 * Create default Auto Shop Categories
 */
function autohub_create_default_auto_shop_categories() {
    // Only run this once
    if (get_option('autohub_auto_shop_categories_created')) {
        return;
    }

    $default_categories = array(
        'Garage' => 'Full-service automotive repair facilities',
        'Body Shop' => 'Collision repair, painting, and bodywork specialists',
        'Dealership' => 'Authorized vehicle dealers and service centers',
        'Parts Seller' => 'Automotive parts and accessories retailers',
        'Service Centre' => 'General automotive service and maintenance providers',
    );

    foreach ($default_categories as $name => $description) {
        if (!term_exists($name, 'auto_shop_category')) {
            wp_insert_term($name, 'auto_shop_category', array(
                'description' => $description,
                'slug' => sanitize_title($name),
            ));
        }
    }

    // Mark as created
    update_option('autohub_auto_shop_categories_created', true);
}
add_action('init', 'autohub_create_default_auto_shop_categories', 20);

/**
 * Register custom post type 'vehicle_professional' (unified: mechanics and body specialists)
 */
function autohub_register_vehicle_professional_post_type() {
    // Vehicle Professional Post Type (unified structure)
    register_post_type('vehicle_professional', array(
        'labels' => array(
            'name' => 'Vehicle Professionals',
            'singular_name' => 'Vehicle Professional',
            'menu_name' => 'Vehicle Professionals',
            'add_new' => 'Add New Professional',
            'add_new_item' => 'Add New Vehicle Professional',
            'edit_item' => 'Edit Vehicle Professional',
            'new_item' => 'New Vehicle Professional',
            'view_item' => 'View Vehicle Professional',
            'search_items' => 'Search Vehicle Professionals',
            'not_found' => 'No vehicle professionals found',
            'not_found_in_trash' => 'No vehicle professionals found in trash'
        ),
        'public' => true,
        'hierarchical' => false,
        'supports' => array('title', 'editor', 'thumbnail'),
        'has_archive' => true,
        'rewrite' => array('slug' => 'vehicle-professionals'),
        'menu_icon' => 'dashicons-admin-users',
        'menu_position' => 28,
        'capability_type' => 'vehicle_professional',
        'map_meta_cap' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'autohub_register_vehicle_professional_post_type', 0);

/**
 * Add custom admin columns for listing post type
 */
function autohub_listing_admin_columns($columns) {
    // Remove date column and add our custom columns
    unset($columns['date']);
    
    $columns['linked_auto_shop'] = __('Auto Shop', 'autohubzambia');
    $columns['part_number'] = __('Part Number', 'autohubzambia');
    $columns['brand_name'] = __('Brand', 'autohubzambia');
    $columns['stock_status'] = __('Stock Status', 'autohubzambia');
    $columns['date'] = __('Date', 'autohubzambia'); // Add date back at the end
    
    return $columns;
}
add_filter('manage_listing_posts_columns', 'autohub_listing_admin_columns');

/**
 * Populate custom admin columns for listing post type
 */
function autohub_listing_admin_column_content($column, $post_id) {
    switch ($column) {
        case 'linked_auto_shop':
            $auto_shop = get_field('linked_auto_shop', $post_id);
            if ($auto_shop) {
                // ACF relationship field returns array of post objects
                if (is_array($auto_shop) && !empty($auto_shop)) {
                    $auto_shop_obj = $auto_shop[0]; // Get first (and only) auto shop
                    $auto_shop_title = $auto_shop_obj->post_title;
                    $auto_shop_link = get_edit_post_link($auto_shop_obj->ID);
                    echo $auto_shop_link ? '<a href="' . esc_url($auto_shop_link) . '">' . esc_html($auto_shop_title) . '</a>' : esc_html($auto_shop_title);
                } elseif (is_object($auto_shop)) {
                    // Single post object
                    $auto_shop_title = $auto_shop->post_title;
                    $auto_shop_link = get_edit_post_link($auto_shop->ID);
                    echo $auto_shop_link ? '<a href="' . esc_url($auto_shop_link) . '">' . esc_html($auto_shop_title) . '</a>' : esc_html($auto_shop_title);
                } else {
                    echo '—';
                }
            } else {
                echo '—';
            }
            break;
            
        case 'part_number':
            $part_number = get_post_meta($post_id, 'part_number', true);
            echo $part_number ? esc_html($part_number) : '—';
            break;
            
        case 'brand_name':
            $brand_name = get_post_meta($post_id, 'brand_name', true);
            echo $brand_name ? esc_html($brand_name) : '—';
            break;
            
        case 'stock_status':
            $stock_status = get_post_meta($post_id, 'stock_status', true);
            if ($stock_status) {
                $status_class = '';
                switch ($stock_status) {
                    case 'In Stock':
                        $status_class = 'style="color: #46a049; font-weight: bold;"';
                        break;
                    case 'Out of Stock':
                        $status_class = 'style="color: #f44336; font-weight: bold;"';
                        break;
                    case 'Expected':
                        $status_class = 'style="color: #ff9800; font-weight: bold;"';
                        break;
                }
                echo '<span ' . $status_class . '>' . esc_html($stock_status) . '</span>';
            } else {
                echo '—';
            }
            break;
    }
}
add_action('manage_listing_posts_custom_column', 'autohub_listing_admin_column_content', 10, 2);

/**
 * Make custom columns sortable
 */
function autohub_listing_sortable_columns($columns) {
    $columns['linked_auto_shop'] = 'linked_auto_shop';
    $columns['part_number'] = 'part_number';
    $columns['brand_name'] = 'brand_name';
    $columns['stock_status'] = 'stock_status';
    return $columns;
}
add_filter('manage_edit-listing_sortable_columns', 'autohub_listing_sortable_columns');

/**
 * Handle sorting for custom columns
 */
function autohub_listing_column_orderby($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    $orderby = $query->get('orderby');

    if ('linked_auto_shop' === $orderby) {
        $query->set('meta_key', 'linked_auto_shop');
        $query->set('orderby', 'meta_value_num');
    } elseif ('part_number' === $orderby) {
        $query->set('meta_key', 'part_number');
        $query->set('orderby', 'meta_value');
    } elseif ('brand_name' === $orderby) {
        $query->set('meta_key', 'brand_name');
        $query->set('orderby', 'meta_value');
    } elseif ('stock_status' === $orderby) {
        $query->set('meta_key', 'stock_status');
        $query->set('orderby', 'meta_value');
    }
}
add_action('pre_get_posts', 'autohub_listing_column_orderby');

/**
 * Helper function to get listings for a specific auto shop
 *
 * @param int $auto_shop_id The auto shop post ID
 * @param array $args Additional WP_Query arguments
 * @return WP_Query
 */
function autohub_get_listings_by_auto_shop($auto_shop_id, $args = array()) {
    $default_args = array(
        'post_type' => 'listing',
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key' => 'linked_auto_shop',
                'value' => '"' . $auto_shop_id . '"', // ACF relationship fields store as serialized arrays
                'compare' => 'LIKE'
            ),
            array(
                'key' => 'linked_auto_shop',
                'value' => $auto_shop_id,
                'compare' => '='
            )
        ),
        'post_status' => 'publish',
        'posts_per_page' => -1, // Get all listings by default
    );
    
    $query_args = wp_parse_args($args, $default_args);
    
    return new WP_Query($query_args);
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use autohub_get_listings_by_auto_shop() instead
 */
function autohub_get_listings_by_service_center($service_center_id, $args = array()) {
    return autohub_get_listings_by_auto_shop($service_center_id, $args);
}

/**
 * Helper function to get the auto shop for a specific listing
 *
 * @param int $listing_id The listing post ID
 * @return WP_Post|null The auto shop post object or null if not found
 */
function autohub_get_listing_auto_shop($listing_id) {
    $auto_shop = get_field('linked_auto_shop', $listing_id);
    
    if ($auto_shop) {
        // ACF relationship field returns array of post objects
        if (is_array($auto_shop) && !empty($auto_shop)) {
            return $auto_shop[0]; // Return first (and only) auto shop
        } elseif (is_object($auto_shop)) {
            return $auto_shop; // Return single post object
        }
    }
    
    return null;
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use autohub_get_listing_auto_shop() instead
 */
function autohub_get_listing_service_center($listing_id) {
    return autohub_get_listing_auto_shop($listing_id);
}

/**
 * Helper function to check if a listing belongs to a specific autoshop
 *
 * @param int $listing_id The listing post ID
 * @param int $shop_id The autoshop post ID
 * @return bool
 */
function autohub_listing_belongs_to_autoshop($listing_id, $shop_id) {
    $autoshop_id = get_post_meta($listing_id, 'autoshop_ref', true);
    return (int) $autoshop_id === (int) $shop_id;
}

/**
 * Register Professional Type taxonomy for vehicle_professional
 */
function autohub_register_professional_type_taxonomy() {
    $labels = array(
        'name' => 'Professional Types',
        'singular_name' => 'Professional Type',
        'menu_name' => 'Professional Types',
        'all_items' => 'All Types',
        'edit_item' => 'Edit Type',
        'view_item' => 'View Type',
        'update_item' => 'Update Type',
        'add_new_item' => 'Add New Type',
        'new_item_name' => 'New Type Name',
        'search_items' => 'Search Types',
        'popular_items' => 'Popular Types',
        'not_found' => 'No types found',
    );

    register_taxonomy('professional_type', array('vehicle_professional'), array(
        'labels' => $labels,
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array(
            'slug' => 'professional-type',
            'with_front' => false,
        ),
        'query_var' => true,
        'meta_box_cb' => 'post_categories_meta_box', // Single select like categories
    ));
}
add_action('init', 'autohub_register_professional_type_taxonomy', 0);

/**
 * Register Specialisations taxonomy for vehicle_professional
 */
function autohub_register_specialisations_taxonomy() {
    $labels = array(
        'name' => 'Specialisations',
        'singular_name' => 'Specialisation',
        'menu_name' => 'Specialisations',
        'all_items' => 'All Specialisations',
        'edit_item' => 'Edit Specialisation',
        'view_item' => 'View Specialisation',
        'update_item' => 'Update Specialisation',
        'add_new_item' => 'Add New Specialisation',
        'new_item_name' => 'New Specialisation Name',
        'search_items' => 'Search Specialisations',
        'popular_items' => 'Popular Specialisations',
        'separate_items_with_commas' => 'Separate specialisations with commas',
        'add_or_remove_items' => 'Add or remove specialisations',
        'choose_from_most_used' => 'Choose from the most used specialisations',
        'not_found' => 'No specialisations found',
    );

    register_taxonomy('specialisations', array('vehicle_professional'), array(
        'labels' => $labels,
        'hierarchical' => false, // Like tags for multi-select
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_rest' => true,
        'rewrite' => array(
            'slug' => 'specialisations',
            'with_front' => false,
        ),
        'query_var' => true,
        'meta_box_cb' => 'post_tags_meta_box', // Multi-select like tags
    ));
}
add_action('init', 'autohub_register_specialisations_taxonomy', 0);

/**
 * Create default Professional Types
 */
function autohub_create_default_professional_types() {
    // Only run this once
    if (get_option('autohub_professional_types_created')) {
        return;
    }

    $default_types = array(
        'Mechanic' => 'General automotive repair and maintenance specialists',
        'Body Specialist' => 'Collision repair, painting, and bodywork specialists',
    );

    foreach ($default_types as $name => $description) {
        if (!term_exists($name, 'professional_type')) {
            wp_insert_term($name, 'professional_type', array(
                'description' => $description,
                'slug' => sanitize_title($name),
            ));
        }
    }

    // Mark as created
    update_option('autohub_professional_types_created', true);
}
add_action('init', 'autohub_create_default_professional_types', 20);

/**
 * Create default Specialisations
 */
function autohub_create_default_specialisations() {
    // Only run this once
    if (get_option('autohub_specialisations_created')) {
        return;
    }

    $default_specialisations = array(
        'German Cars',
        'Japanese Cars', 
        'Hybrid & EV',
        'Dent Repair',
        'Resprays',
        'Engine Overhauls',
        'Brake Systems',
        'Suspension',
        'Transmission',
        'Air Conditioning',
        'Electrical Systems',
        'Diesel Engines',
        'Turbo Systems',
        'Classic Cars',
        'Commercial Vehicles',
        'Motorcycles'
    );

    foreach ($default_specialisations as $name) {
        if (!term_exists($name, 'specialisations')) {
            wp_insert_term($name, 'specialisations', array(
                'slug' => sanitize_title($name),
            ));
        }
    }

    // Mark as created
    update_option('autohub_specialisations_created', true);
}
add_action('init', 'autohub_create_default_specialisations', 20);

/**
 * Register custom post type 'listing_plan'
 */
function autohub_register_listing_plan_post_type() {
    register_post_type('listing_plan', array(
        'labels' => array(
            'name' => 'Listing Plans',
            'singular_name' => 'Listing Plan',
            'menu_name' => 'Listing Plans',
            'add_new' => 'Add New Plan',
            'add_new_item' => 'Add New Listing Plan',
            'edit_item' => 'Edit Listing Plan',
            'new_item' => 'New Listing Plan',
            'view_item' => 'View Listing Plan',
            'search_items' => 'Search Listing Plans',
            'not_found' => 'No listing plans found',
            'not_found_in_trash' => 'No listing plans found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'hierarchical' => false,
        'supports' => array('title'),
        'has_archive' => false,
        'rewrite' => array('slug' => 'listing-plan'),
        'menu_icon' => 'dashicons-money-alt',
        'menu_position' => 29,
        'capability_type' => 'listing_plan',
        'map_meta_cap' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'autohub_register_listing_plan_post_type', 0);

/**
 * Register custom post type 'plan_feature'
 */
function autohub_register_plan_feature_post_type() {
    register_post_type('plan_feature', array(
        'labels' => array(
            'name' => 'Plan Features',
            'singular_name' => 'Plan Feature',
            'menu_name' => 'Plan Features',
            'add_new' => 'Add New Feature',
            'add_new_item' => 'Add New Plan Feature',
            'edit_item' => 'Edit Plan Feature',
            'new_item' => 'New Plan Feature',
            'view_item' => 'View Plan Feature',
            'search_items' => 'Search Plan Features',
            'not_found' => 'No plan features found',
            'not_found_in_trash' => 'No plan features found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'hierarchical' => false,
        'supports' => array('title'),
        'has_archive' => false,
        'rewrite' => array('slug' => 'plan-feature'),
        'menu_icon' => 'dashicons-star-filled',
        'menu_position' => 30,
        'capability_type' => 'plan_feature',
        'map_meta_cap' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'autohub_register_plan_feature_post_type', 0);
