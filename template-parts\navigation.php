<?php
/**
 * Template part for displaying navigation
 *
 * @package AutoHub_Zambia
 */
?>

<nav class="nav-sticky nav-gradient nav-shadow sticky top-0 z-50" role="navigation" aria-label="<?php esc_attr_e('Main Navigation', 'autohubzambia'); ?>" id="main-navigation">
    <div class="container-custom" x-data="{ mobileMenuOpen: false }">
        <div class="flex justify-between items-center py-4 transition-all duration-300">
            <!-- Logo -->
            <div class="flex-shrink-0 logo-container">
                <a href="<?php echo esc_url(home_url('/')); ?>" class="flex items-center">
                    <?php 
                    $logo_path = get_template_directory() . '/assets/images/autohubzambia.svg';
                    if (file_exists($logo_path)) : ?>
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/autohubzambia.svg" 
                             alt="<?php bloginfo('name'); ?>" 
                             class="h-12 w-auto transition-all duration-300"
                             id="nav-logo">
                    <?php else : ?>
                        <div class="h-12 flex items-center">
                            <span class="text-2xl font-serif font-bold text-primary">
                                <?php bloginfo('name'); ?>
                            </span>
                        </div>
                    <?php endif; ?>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex items-center space-x-8">
                <a href="<?php echo esc_url(home_url('/')); ?>" class="nav-link <?php echo is_home() || is_front_page() ? 'active' : ''; ?>">
                    <?php esc_html_e('Home', 'autohubzambia'); ?>
                </a>
                
                <!-- About Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="nav-link flex items-center space-x-1 <?php echo is_page(826) || is_page('faqs') ? 'active' : ''; ?>">
                        <span><?php esc_html_e('About', 'autohubzambia'); ?></span>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="open" 
                         x-cloak
                         @click.away="open = false" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-xl py-2 z-50 border border-gray-100">
                        <a href="<?php echo esc_url(get_permalink(826)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                            <?php esc_html_e('About Us', 'autohubzambia'); ?>
                        </a>
                        <a href="<?php echo esc_url(home_url('/faqs/')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                            <?php esc_html_e('FAQs', 'autohubzambia'); ?>
                        </a>
                    </div>
                </div>

                <!-- Auto Shops -->
                <a href="<?php echo esc_url(get_permalink(59)); ?>" class="nav-link <?php echo is_post_type_archive('auto_shop') || is_singular('auto_shop') || is_page('auto-shops') ? 'active' : ''; ?>">
                    <?php esc_html_e('Auto Shops', 'autohubzambia'); ?>
                </a>

                <!-- Vehicle Professionals -->
                <a href="<?php echo esc_url(get_permalink(69)); ?>" class="nav-link <?php echo is_post_type_archive('vehicle_professional') || is_singular('vehicle_professional') || is_page(69) ? 'active' : ''; ?>">
                    <?php esc_html_e('Vehicle Professionals', 'autohubzambia'); ?>
                </a>

                <!-- Parts Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="nav-link flex items-center space-x-1 <?php echo is_page(array(279, 277)) || is_post_type_archive(array('request', 'quote')) ? 'active' : ''; ?>">
                        <span><?php esc_html_e('Parts', 'autohubzambia'); ?></span>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div x-show="open" 
                         x-cloak
                         @click.away="open = false" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-xl py-2 z-50 border border-gray-100">
                        <a href="<?php echo esc_url(get_permalink(279)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                            <?php esc_html_e('Browse Requests', 'autohubzambia'); ?>
                        </a>
                        <a href="<?php echo esc_url(get_permalink(277)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                            <?php esc_html_e('Browse Quotes', 'autohubzambia'); ?>
                        </a>
                        <a href="<?php echo esc_url(get_post_type_archive_link('listing')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                            <?php esc_html_e('Parts Listings', 'autohubzambia'); ?>
                        </a>
                    </div>
                </div>

                <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="nav-link <?php echo is_page('contact') ? 'active' : ''; ?>">
                    <?php esc_html_e('Contact', 'autohubzambia'); ?>
                </a>
                
                <!-- My Account Button -->
                <div class="ml-4">
                    <?php if (is_user_logged_in()) : 
                        $current_user = wp_get_current_user();
                        $user_roles = $current_user->roles;
                        $display_name = $current_user->display_name;
                    ?>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="btn-primary btn-nav flex items-center space-x-2 animate-fade-in-up">
                                <div class="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <span class="text-xs font-medium text-white">
                                        <?php echo esc_html(strtoupper(substr($display_name, 0, 1))); ?>
                                    </span>
                                </div>
                                <span><?php echo esc_html($display_name); ?></span>
                                <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div x-show="open" 
                                 x-cloak
                                 @click.away="open = false" 
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-xl py-2 z-50 border border-gray-100 animate-slide-in-down">
                                
                                <!-- User Info Header -->
                                <div class="px-4 py-3 border-b border-gray-100">
                                    <p class="text-sm font-medium text-gray-900"><?php echo esc_html($display_name); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo esc_html($current_user->user_email); ?></p>
                                    <?php if (in_array('consumer', $user_roles)): ?>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800 mt-1">
                                            Consumer
                                        </span>
                                    <?php elseif (in_array('administrator', $user_roles)): ?>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 mt-1">
                                            Administrator
                                        </span>
                                    <?php elseif (in_array('business_owner', $user_roles)): ?>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-secondary-100 text-secondary-800 mt-1">
                                            Business Owner
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- Dashboard Links -->
                                <?php if (in_array('consumer', $user_roles)): ?>
                                    <a href="<?php echo esc_url(autohub_get_dashboard_url()); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                                        </svg>
                                        <?php esc_html_e('My Dashboard', 'autohubzambia'); ?>
                                    </a>
                                <?php elseif (in_array('auto_shop_owner', $user_roles)): ?>
                                    <a href="<?php echo esc_url(autohub_get_business_dashboard_url()); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-secondary-50 hover:text-secondary-600 transition-colors duration-200 rounded-md mx-2">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        <?php esc_html_e('Business Dashboard', 'autohubzambia'); ?>
                                    </a>
                                <?php endif; ?>
                                
                                <!-- Request/Quote System Links -->
                                <?php 
                                $notifications = autohub_get_user_notifications();
                                if (in_array('consumer', $user_roles)): ?>
                                    <div class="border-t border-gray-100 my-2"></div>
                                    <a href="<?php echo esc_url(get_permalink(269)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                        <?php esc_html_e('My Requests', 'autohubzambia'); ?>
                                        <?php if (isset($notifications['pending_quotes']) && $notifications['pending_quotes'] > 0): ?>
                                            <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full ml-2">
                                                <?php echo esc_html($notifications['pending_quotes']); ?>
                                            </span>
                                        <?php endif; ?>
                                    </a>
                                    <a href="<?php echo esc_url(get_permalink(275)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        <?php esc_html_e('Submit Request', 'autohubzambia'); ?>
                                    </a>
                                <?php elseif (in_array('auto_shop_owner', $user_roles)): ?>
                                    <div class="border-t border-gray-100 my-2"></div>
                                    <a href="<?php echo esc_url(get_permalink(273)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-secondary-50 hover:text-secondary-600 transition-colors duration-200 rounded-md mx-2">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        <?php esc_html_e('My Quotes', 'autohubzambia'); ?>
                                        <?php if (isset($notifications['quote_updates']) && $notifications['quote_updates'] > 0): ?>
                                            <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-green-500 rounded-full ml-2">
                                                <?php echo esc_html($notifications['quote_updates']); ?>
                                            </span>
                                        <?php endif; ?>
                                    </a>
                                    <a href="<?php echo esc_url(get_permalink(279)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-secondary-50 hover:text-secondary-600 transition-colors duration-200 rounded-md mx-2">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                        <?php esc_html_e('Browse Requests', 'autohubzambia'); ?>
                                    </a>
                                <?php endif; ?>
                                
                                <!-- Public Browse Links for all users -->
                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="<?php echo esc_url(get_permalink(277)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    <?php esc_html_e('Browse Quotes', 'autohubzambia'); ?>
                                </a>
                                
                                <!-- WordPress Admin Link for all logged-in users -->
                                <a href="<?php echo esc_url(admin_url()); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <?php esc_html_e('WordPress Admin', 'autohubzambia'); ?>
                                </a>

                                <!-- Profile Link -->
                                <a href="<?php echo esc_url(autohub_get_dashboard_url() . '#profile'); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <?php esc_html_e('My Profile', 'autohubzambia'); ?>
                                </a>

                                <!-- Settings Link -->
                                <a href="<?php echo esc_url(autohub_get_dashboard_url() . '#settings'); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <?php esc_html_e('Settings', 'autohubzambia'); ?>
                                </a>

                                <!-- Divider -->
                                <div class="border-t border-gray-100 my-2"></div>

                                <!-- Logout Link -->
                                <a href="<?php echo esc_url(wp_logout_url(home_url())); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors duration-200 rounded-md mx-2">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    <?php esc_html_e('Logout', 'autohubzambia'); ?>
                                </a>
                            </div>
                        </div>
                    <?php else : ?>
                        <!-- Not Logged In - Show Login/Register Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="btn-primary btn-nav flex items-center space-x-2 animate-fade-in-up">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span><?php esc_html_e('My Account', 'autohubzambia'); ?></span>
                                <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div x-show="open" 
                                 x-cloak
                                 @click.away="open = false" 
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-xl py-2 z-50 border border-gray-100 animate-slide-in-down">
                                
                                <!-- Welcome Message -->
                                <div class="px-4 py-3 border-b border-gray-100">
                                    <p class="text-sm font-medium text-gray-900"><?php esc_html_e('Welcome to AutoHub', 'autohubzambia'); ?></p>
                                    <p class="text-xs text-gray-500"><?php esc_html_e('Sign in to access your account', 'autohubzambia'); ?></p>
                                </div>

                                <!-- Login Link -->
                                <a href="<?php echo esc_url(wp_login_url(get_permalink())); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                    </svg>
                                    <?php esc_html_e('Sign In', 'autohubzambia'); ?>
                                </a>

                                <!-- Register as Consumer -->
                                <a href="<?php echo esc_url(autohub_get_register_url()); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary transition-colors duration-200 rounded-md mx-2">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                    </svg>
                                    <?php esc_html_e('Register as Consumer', 'autohubzambia'); ?>
                                </a>

                                <!-- Register Business -->
                                <a href="<?php echo esc_url(autohub_get_business_register_url()); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-secondary-50 hover:text-secondary-600 transition-colors duration-200 rounded-md mx-2">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <?php esc_html_e('Register Your Business', 'autohubzambia'); ?>
                                </a>

                                <!-- Resend Verification Email -->
                                <a href="<?php echo esc_url(get_permalink(312)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-colors duration-200 rounded-md mx-2">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    <?php esc_html_e('Resend Verification Email', 'autohubzambia'); ?>
                                </a>

                                <!-- Divider -->
                                <div class="border-t border-gray-100 my-2"></div>

                                <!-- Quick Info -->
                                <div class="px-4 py-2">
                                    <p class="text-xs text-gray-500 mb-2"><?php esc_html_e('Join AutoHub to:', 'autohubzambia'); ?></p>
                                    <ul class="text-xs text-gray-600 space-y-1">
                                        <li class="flex items-center">
                                            <svg class="w-3 h-3 mr-1 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <?php esc_html_e('Find auto parts & services', 'autohubzambia'); ?>
                                        </li>
                                        <li class="flex items-center">
                                            <svg class="w-3 h-3 mr-1 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <?php esc_html_e('Manage your vehicles', 'autohubzambia'); ?>
                                        </li>
                                        <li class="flex items-center">
                                            <svg class="w-3 h-3 mr-1 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <?php esc_html_e('Grow your business', 'autohubzambia'); ?>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Mobile menu button -->
            <div class="lg:hidden">
                <button type="button" 
                        class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-lg text-gray-700 hover:text-primary hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-all duration-200" 
                        aria-controls="mobile-menu" 
                        :aria-expanded="mobileMenuOpen" 
                        @click="mobileMenuOpen = !mobileMenuOpen">
                    <span class="sr-only"><?php esc_html_e('Open main menu', 'autohubzambia'); ?></span>
                    <!-- Hamburger icon -->
                    <svg x-show="!mobileMenuOpen" 
                         x-transition:enter="transition ease-in duration-150"
                         x-transition:enter-start="opacity-0 rotate-90"
                         x-transition:enter-end="opacity-100 rotate-0"
                         class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    <!-- Close icon -->
                    <svg x-show="mobileMenuOpen" 
                         x-transition:enter="transition ease-in duration-150"
                         x-transition:enter-start="opacity-0 -rotate-90"
                         x-transition:enter-end="opacity-100 rotate-0"
                         class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="mobile-menu lg:hidden" 
             x-show="mobileMenuOpen"
             x-cloak
             @click.away="mobileMenuOpen = false"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200 shadow-lg">
                <a href="<?php echo esc_url(home_url('/')); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-primary font-medium rounded-lg <?php echo is_home() || is_front_page() ? 'active text-primary bg-primary-50' : ''; ?>" @click="mobileMenuOpen = false">
                    <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    <?php esc_html_e('Home', 'autohubzambia'); ?>
                </a>
                
                <!-- About Section -->
                <div class="mobile-nav-section">
                    <div class="mobile-nav-header px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                        <?php esc_html_e('About', 'autohubzambia'); ?>
                    </div>
                    <a href="<?php echo esc_url(get_permalink(826)); ?>" class="mobile-nav-item block px-6 py-2 text-gray-700 hover:text-primary font-medium rounded-lg <?php echo is_page(826) ? 'active text-primary bg-primary-50' : ''; ?>" @click="mobileMenuOpen = false">
                        <svg class="w-4 h-4 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <?php esc_html_e('About Us', 'autohubzambia'); ?>
                    </a>
                    <a href="<?php echo esc_url(home_url('/faqs/')); ?>" class="mobile-nav-item block px-6 py-2 text-gray-700 hover:text-primary font-medium rounded-lg <?php echo is_page('faqs') ? 'active text-primary bg-primary-50' : ''; ?>" @click="mobileMenuOpen = false">
                        <svg class="w-4 h-4 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <?php esc_html_e('FAQs', 'autohubzambia'); ?>
                    </a>
                </div>

                <!-- Auto Shops Section -->
                <div class="mobile-nav-section">
                    <a href="<?php echo esc_url(get_permalink(59)); ?>" class="mobile-nav-item block px-6 py-2 text-gray-700 hover:text-primary font-medium rounded-lg <?php echo is_post_type_archive('auto_shop') || is_singular('auto_shop') || is_page('auto-shops') ? 'active text-primary bg-primary-50' : ''; ?>" @click="mobileMenuOpen = false">
                        <svg class="w-4 h-4 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <?php esc_html_e('Auto Shops', 'autohubzambia'); ?>
                    </a>
                </div>

                <!-- Vehicle Professionals Section -->
                <div class="mobile-nav-section">
                    <a href="<?php echo esc_url(get_permalink(69)); ?>" class="mobile-nav-item block px-6 py-2 text-gray-700 hover:text-primary font-medium rounded-lg <?php echo is_post_type_archive('vehicle_professional') || is_singular('vehicle_professional') || is_page(69) ? 'active text-primary bg-primary-50' : ''; ?>" @click="mobileMenuOpen = false">
                        <svg class="w-4 h-4 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <?php esc_html_e('Vehicle Professionals', 'autohubzambia'); ?>
                    </a>
                </div>

                <!-- Parts Section -->
                <div class="mobile-nav-section">
                    <div class="mobile-nav-header px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                        <?php esc_html_e('Parts', 'autohubzambia'); ?>
                    </div>
                    <a href="<?php echo esc_url(get_permalink(279)); ?>" class="mobile-nav-item block px-6 py-2 text-gray-700 hover:text-primary font-medium rounded-lg <?php echo is_page(279) ? 'active text-primary bg-primary-50' : ''; ?>" @click="mobileMenuOpen = false">
                        <svg class="w-4 h-4 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <?php esc_html_e('Browse Requests', 'autohubzambia'); ?>
                    </a>
                    <a href="<?php echo esc_url(get_permalink(277)); ?>" class="mobile-nav-item block px-6 py-2 text-gray-700 hover:text-primary font-medium rounded-lg <?php echo is_page(277) ? 'active text-primary bg-primary-50' : ''; ?>" @click="mobileMenuOpen = false">
                        <svg class="w-4 h-4 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        <?php esc_html_e('Browse Quotes', 'autohubzambia'); ?>
                    </a>
                    <a href="<?php echo esc_url(get_post_type_archive_link('listing')); ?>" class="mobile-nav-item block px-6 py-2 text-gray-700 hover:text-primary font-medium rounded-lg" @click="mobileMenuOpen = false">
                        <svg class="w-4 h-4 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        <?php esc_html_e('Parts Listings', 'autohubzambia'); ?>
                    </a>
                </div>

                <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-primary font-medium rounded-lg <?php echo is_page('contact') ? 'active text-primary bg-primary-50' : ''; ?>" @click="mobileMenuOpen = false">
                    <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <?php esc_html_e('Contact', 'autohubzambia'); ?>
                </a>
                
                <!-- Mobile My Account -->
                <div class="border-t border-gray-200 pt-3 mt-3">
                    <?php if (is_user_logged_in()) : 
                        $current_user = wp_get_current_user();
                        $user_roles = $current_user->roles;
                        $display_name = $current_user->display_name;
                    ?>
                        <!-- User Info -->
                        <div class="px-3 py-2 mb-2">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                    <span class="text-primary-600 font-medium text-sm">
                                        <?php echo esc_html(strtoupper(substr($display_name, 0, 1))); ?>
                                    </span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900"><?php echo esc_html($display_name); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo esc_html($current_user->user_email); ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- Dashboard Link -->
                        <?php if (in_array('consumer', $user_roles)): ?>
                            <a href="<?php echo esc_url(autohub_get_dashboard_url()); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-primary font-medium rounded-lg" @click="mobileMenuOpen = false">
                                <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                                </svg>
                                <?php esc_html_e('My Dashboard', 'autohubzambia'); ?>
                            </a>
                        <?php elseif (in_array('auto_shop_owner', $user_roles)): ?>
                            <a href="<?php echo esc_url(autohub_get_business_dashboard_url()); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-secondary-600 font-medium rounded-lg" @click="mobileMenuOpen = false">
                                <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <?php esc_html_e('Business Dashboard', 'autohubzambia'); ?>
                            </a>
                        <?php elseif (in_array('administrator', $user_roles)): ?>
                            <a href="<?php echo esc_url(admin_url()); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-primary font-medium rounded-lg" @click="mobileMenuOpen = false">
                                <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <?php esc_html_e('Admin Dashboard', 'autohubzambia'); ?>
                            </a>
                        <?php endif; ?>
                        
                        <!-- Request/Quote System Links -->
                        <?php if (in_array('consumer', $user_roles)): ?>
                            <a href="<?php echo esc_url(get_permalink(269)); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-primary font-medium rounded-lg" @click="mobileMenuOpen = false">
                                <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                <?php esc_html_e('My Requests', 'autohubzambia'); ?>
                            </a>
                            <a href="<?php echo esc_url(get_permalink(275)); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-primary font-medium rounded-lg" @click="mobileMenuOpen = false">
                                <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                <?php esc_html_e('Submit Request', 'autohubzambia'); ?>
                            </a>
                        <?php elseif (in_array('auto_shop_owner', $user_roles)): ?>
                            <a href="<?php echo esc_url(get_permalink(273)); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-secondary-600 font-medium rounded-lg" @click="mobileMenuOpen = false">
                                <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                <?php esc_html_e('My Quotes', 'autohubzambia'); ?>
                            </a>
                            <a href="<?php echo esc_url(get_permalink(279)); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-secondary-600 font-medium rounded-lg" @click="mobileMenuOpen = false">
                                <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <?php esc_html_e('Browse Requests', 'autohubzambia'); ?>
                            </a>
                        <?php endif; ?>
                        
                        <!-- WordPress Admin Link for all logged-in users -->
                        <a href="<?php echo esc_url(admin_url()); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-primary font-medium rounded-lg" @click="mobileMenuOpen = false">
                            <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <?php esc_html_e('WordPress Admin', 'autohubzambia'); ?>
                        </a>

                        <!-- Profile Link -->
                        <a href="<?php echo esc_url(autohub_get_dashboard_url() . '#profile'); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-primary font-medium rounded-lg" @click="mobileMenuOpen = false">
                            <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <?php esc_html_e('My Profile', 'autohubzambia'); ?>
                        </a>

                        <!-- Settings Link -->
                        <a href="<?php echo esc_url(autohub_get_dashboard_url() . '#settings'); ?>" class="mobile-nav-item block px-3 py-3 text-gray-700 hover:text-primary font-medium rounded-lg" @click="mobileMenuOpen = false">
                            <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <?php esc_html_e('Settings', 'autohubzambia'); ?>
                        </a>

                        <!-- Logout Link -->
                        <a href="<?php echo esc_url(wp_logout_url(home_url())); ?>" class="mobile-nav-item block px-3 py-3 text-red-600 hover:text-red-700 font-medium rounded-lg">
                            <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            <?php esc_html_e('Logout', 'autohubzambia'); ?>
                        </a>
                    <?php else : ?>
                        <!-- Not Logged In - Show Login/Register Options -->
                        <div class="px-3 py-2 mb-2">
                            <p class="text-sm font-medium text-gray-900 mb-1"><?php esc_html_e('Welcome to AutoHub', 'autohubzambia'); ?></p>
                            <p class="text-xs text-gray-500"><?php esc_html_e('Sign in to access your account', 'autohubzambia'); ?></p>
                        </div>

                        <!-- Login Link -->
                        <a href="<?php echo esc_url(wp_login_url(get_permalink())); ?>" class="mobile-nav-item block px-3 py-3 text-primary hover:bg-primary-50 font-medium rounded-lg">
                            <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                            <?php esc_html_e('Sign In', 'autohubzambia'); ?>
                        </a>

                        <!-- Register Link -->
                        <a href="<?php echo esc_url(autohub_get_register_url()); ?>" class="mobile-nav-item block px-3 py-3 text-secondary-600 hover:bg-secondary-50 font-medium rounded-lg">
                            <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                            </svg>
                            <?php esc_html_e('Create Account', 'autohubzambia'); ?>
                        </a>

                        <!-- Resend Verification Email -->
                        <a href="<?php echo esc_url(get_permalink(312)); ?>" class="mobile-nav-item block px-3 py-3 text-orange-600 hover:bg-orange-50 font-medium rounded-lg">
                            <svg class="w-5 h-5 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <?php esc_html_e('Resend Verification Email', 'autohubzambia'); ?>
                        </a>

                        <!-- Benefits Info -->
                        <div class="px-3 py-2 mt-2 bg-gray-50 rounded-lg">
                            <p class="text-xs text-gray-500 mb-2"><?php esc_html_e('Join AutoHub to:', 'autohubzambia'); ?></p>
                            <ul class="text-xs text-gray-600 space-y-1">
                                <li class="flex items-center">
                                    <svg class="w-3 h-3 mr-2 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php esc_html_e('Find auto parts & services', 'autohubzambia'); ?>
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-3 h-3 mr-2 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php esc_html_e('Manage your vehicles', 'autohubzambia'); ?>
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-3 h-3 mr-2 text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php esc_html_e('Connect with professionals', 'autohubzambia'); ?>
                                </li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
// Enhanced navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    const nav = document.getElementById('main-navigation');
    const logo = document.getElementById('nav-logo');
    
    // Scroll effect for navigation
    let lastScrollTop = 0;
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            nav.classList.add('scrolled');
        } else {
            nav.classList.remove('scrolled');
        }
        
        lastScrollTop = scrollTop;
    });
    
    // Add loading animation to navigation links
    const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-item');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.href && this.href !== window.location.href) {
                this.classList.add('nav-loading');
            }
        });
    });
});
</script>